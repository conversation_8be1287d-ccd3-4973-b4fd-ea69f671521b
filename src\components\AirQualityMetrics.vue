<template>
  <div class="metrics-section">
    <div class="metrics-card">
      <h3>空气质量指标</h3>
      
      <div class="metrics-grid">
        <div 
          v-for="metric in metrics" 
          :key="metric.id"
          class="metric-item"
        >
          <div :class="['metric-value', metric.color]">
            {{ metric.value }}
          </div>
          <div class="metric-label">
            {{ metric.label }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Metric {
  id: string
  value: string
  label: string
  color: string
}

interface Props {
  metrics: Metric[]
}

defineProps<Props>()
</script>

<style scoped>
.metrics-section {
  @apply mb-6;
}

.metrics-card {
  @apply bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg;
}

.metrics-card h3 {
  @apply text-lg font-semibold text-gray-800 mb-4;
}

.metrics-grid {
  @apply grid grid-cols-3 gap-4;
}

.metric-item {
  @apply text-center p-4 bg-gray-50 rounded-lg;
}

.metric-value {
  @apply text-2xl font-bold mb-2;
}

.metric-label {
  @apply text-xs text-gray-600;
}
</style>
