<template>
  <div class="health-protection">
    <span class="protection-text">已为您健康守护 </span>
    <span class="days-number">{{ days }}</span>
    <span class="protection-text"> 天</span>
  </div>
</template>

<script setup lang="ts">
interface Props {
  days: number
}

defineProps<Props>()
</script>

<style scoped>
.health-protection {
  @apply top-64 left-[109px] text-transparent text-[15px] absolute font-normal tracking-[0] leading-[normal];
  font-family: 'HarmonyOS_Sans-Regular', Helvetica;
}

.protection-text {
  @apply text-[#5b5b5b];
}

.days-number {
  @apply text-[#5b5b5b] text-[19px];
}
</style>
