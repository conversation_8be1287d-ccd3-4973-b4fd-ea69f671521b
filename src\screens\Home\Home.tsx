import React from "react";
import { Button } from "../../components/ui/button";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "../../components/ui/card";

export const Home = (): JSX.Element => {
  const openReactVersion = () => {
    window.open('/react-index.html', '_blank');
  };

  const openVueVersion = () => {
    window.open('/vue-index.html', '_blank');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-green-50 to-orange-50 flex items-center justify-center p-4">
      <div className="max-w-4xl w-full">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-800 mb-4">
            空气质量监控系统
          </h1>
          <p className="text-lg text-gray-600">
            选择您想要查看的版本
          </p>
        </div>

        <div className="grid md:grid-cols-2 gap-8">
          {/* React Version Card */}
          <Card className="hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
            <CardHeader className="text-center">
              <CardTitle className="text-2xl text-blue-600 flex items-center justify-center gap-2">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M14.23 12.004a2.236 2.236 0 0 1-2.235 2.236 2.236 2.236 0 0 1-2.236-2.236 2.236 2.236 0 0 1 2.235-2.236 2.236 2.236 0 0 1 2.236 2.236zm2.648-10.69c-1.346 0-3.107.96-4.888 2.622-1.78-1.653-3.542-2.602-4.887-2.602-.41 0-.783.093-1.106.278-1.375.793-1.683 3.264-.973 6.365C1.98 8.917 0 10.42 0 12.004c0 1.59 1.99 3.097 5.043 4.03-.704 3.113-.39 5.588.988 6.38.32.187.69.275 1.102.275 1.345 0 3.107-.96 4.888-2.624 1.78 1.654 3.542 2.603 4.887 2.603.41 0 .783-.09 1.106-.275 1.374-.792 1.683-3.263.973-6.365C22.02 15.096 24 13.59 24 12.004c0-1.59-1.99-3.097-5.043-4.032.704-3.11.39-5.587-.988-6.38-.318-.184-.688-.277-1.092-.278zm-.005 1.09v.006c.225 0 .406.044.558.127.666.382.955 1.835.73 3.704-.054.46-.142.945-.25 1.44-.96-.236-2.006-.417-3.107-.534-.66-.905-1.345-1.727-2.035-2.447 1.592-1.48 3.087-2.292 4.105-2.295zm-9.77.02c1.012 0 2.514.808 4.11 2.28-.686.72-1.37 1.537-2.02 2.442-1.107.117-2.154.298-3.113.538-.112-.49-.195-.964-.254-1.42-.23-1.868.054-3.32.714-3.707.19-.09.4-.127.563-.132zm4.882 3.05c.455.468.91.992 1.36 1.564-.44-.02-.89-.034-1.345-.034-.46 0-.915.01-1.36.034.44-.572.895-1.096 1.345-1.565zM12 8.1c.74 0 1.477.034 2.202.093.406.582.802 1.203 1.183 1.86.372.64.71 1.29 1.018 1.946-.308.655-.646 1.31-1.013 1.95-.38.66-.773 1.288-1.18 1.87-.728.063-1.466.098-2.21.098-.74 0-1.477-.035-2.202-.093-.406-.582-.802-1.204-1.183-1.86-.372-.64-.71-1.29-1.018-1.946.303-.657.646-1.313 1.013-1.954.38-.66.773-1.286 1.18-1.868.728-.064 1.466-.098 2.21-.098zm-3.635.254c-.24.377-.48.763-.704 1.16-.225.39-.435.782-.635 1.174-.265-.656-.49-1.31-.676-1.947.64-.15 1.315-.283 2.015-.386zm7.26 0c.695.103 1.365.23 2.006.387-.18.632-.405 1.282-.66 1.933-.2-.39-.41-.783-.64-1.174-.225-.392-.465-.774-.705-1.146zm3.063.675c.484.15.944.317 1.375.498 1.732.74 2.852 1.708 2.852 2.476-.005.768-1.125 1.74-2.857 2.475-.42.18-.88.342-1.355.493-.28-.958-.646-1.956-1.1-2.98.45-1.017.81-2.01 1.085-2.964zm-13.395.004c.278.96.645 1.957 1.1 2.98-.45 1.017-.812 2.01-1.086 2.964-.484-.15-.944-.318-1.37-.5-1.732-.737-2.852-1.706-2.852-2.474 0-.768 1.12-1.742 2.852-2.476.42-.18.88-.342 1.356-.494zm11.678 4.28c.265.657.49 1.312.676 1.948-.64.157-1.316.29-2.016.39.24-.375.48-.762.705-1.158.225-.39.435-.788.636-1.18zm-9.945.02c.2.392.41.783.64 1.175.23.39.465.772.705 1.143-.695-.102-1.365-.23-2.006-.386.18-.63.406-1.282.66-1.933zM17.92 16.32c.112.493.2.968.254 1.423.23 1.868-.054 3.32-.714 3.708-.147.09-.338.128-.563.128-1.012 0-2.514-.807-4.11-2.28.686-.72 1.37-1.536 2.02-2.44 1.107-.118 2.154-.3 3.113-.54zm-11.83.01c.96.234 2.006.415 3.107.532.66.905 1.345 1.727 2.035 2.446-1.595 1.483-3.092 2.295-4.11 2.295-.22-.005-.406-.05-.553-.132-.666-.38-.955-1.834-.73-3.703.054-.46.142-.944.25-1.438zm4.56.64c.************ 1.345.034.46 0 .915-.01 1.36-.034-.44.572-.895 1.095-1.345 1.565-.455-.47-.91-.993-1.36-1.565z"/>
                </svg>
                React 版本
              </CardTitle>
            </CardHeader>
            <CardContent className="text-center space-y-4">
              <p className="text-gray-600">
                使用 React + TypeScript + Tailwind CSS 构建的原始版本
              </p>
              <div className="space-y-2 text-sm text-gray-500">
                <div>✓ React 18</div>
                <div>✓ TypeScript</div>
                <div>✓ Shadcn/ui 组件</div>
                <div>✓ Lucide React 图标</div>
              </div>
              <Button 
                onClick={openReactVersion}
                className="w-full bg-blue-600 hover:bg-blue-700 text-white"
              >
                查看 React 版本
              </Button>
            </CardContent>
          </Card>

          {/* Vue Version Card */}
          <Card className="hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
            <CardHeader className="text-center">
              <CardTitle className="text-2xl text-green-600 flex items-center justify-center gap-2">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M24,1.61H14.06L12,5.16,9.94,1.61H0L12,22.39ZM12,14.08,5.16,2.23H9.59L12,6.41l2.41-4.18h4.43Z"/>
                </svg>
                Vue 版本
              </CardTitle>
            </CardHeader>
            <CardContent className="text-center space-y-4">
              <p className="text-gray-600">
                使用 Vue 3 + TypeScript + Tailwind CSS 重新构建的版本
              </p>
              <div className="space-y-2 text-sm text-gray-500">
                <div>✓ Vue 3 Composition API</div>
                <div>✓ TypeScript</div>
                <div>✓ 自定义动画效果</div>
                <div>✓ 响应式设计</div>
              </div>
              <Button 
                onClick={openVueVersion}
                className="w-full bg-green-600 hover:bg-green-700 text-white"
              >
                查看 Vue 版本
              </Button>
            </CardContent>
          </Card>
        </div>

        <div className="mt-12 text-center">
          <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg">
            <h3 className="text-xl font-semibold text-gray-800 mb-4">功能特性</h3>
            <div className="grid md:grid-cols-3 gap-4 text-sm text-gray-600">
              <div className="space-y-2">
                <div className="font-medium text-blue-600">实时监控</div>
                <div>PM2.5、甲醛、负氧离子实时数据</div>
              </div>
              <div className="space-y-2">
                <div className="font-medium text-green-600">智能控制</div>
                <div>设备开关、风速调节、运行状态</div>
              </div>
              <div className="space-y-2">
                <div className="font-medium text-orange-600">天气信息</div>
                <div>温度、湿度、空气质量指数</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
