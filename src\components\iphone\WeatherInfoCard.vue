<template>
  <div class="weather-card">
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <div class="loading-spinner"></div>
      <div class="loading-text">加载中...</div>
    </div>

    <!-- 天气内容 -->
    <div v-else class="weather-content">
      <!-- 刷新按钮 -->
      <button @click="$emit('refresh')" class="refresh-btn">
        <svg class="refresh-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M4 4V9H4.58152M4.58152 9C5.24618 7.35652 6.43101 5.9604 7.96 5.05C9.48899 4.1396 11.2943 3.75 13.1152 3.94148C14.9361 4.13295 16.6618 4.89479 18.0434 6.12C19.4251 7.34521 20.3898 8.97953 20.8 10.78M4.58152 9H9M20 20V15H19.4185M19.4185 15C18.7538 16.6435 17.569 18.0396 16.04 18.95C14.511 19.8604 12.7057 20.25 10.8848 20.0585C9.06393 19.867 7.33818 19.1052 5.95655 17.88C4.57493 16.6548 3.61015 15.0205 3.2 13.22M19.4185 15H15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
        刷新
      </button>

      <!-- 顶部信息行 -->
      <div class="top-row">
        <!-- 城市和时间 -->
        <div class="city-info">
          <div class="city-name">{{ weatherData?.city || "深圳市" }}</div>
          <div class="publish-info">
            {{ formatDate(weatherData?.publish_time) }} {{ formatTime(weatherData?.publish_time) }}发布
          </div>
        </div>

        <!-- 温度 -->
        <div class="temperature">
          <span class="temp-number">{{ weatherData?.temperature || 33 }}</span>
          <span class="temp-symbol">°C</span>
        </div>

        <!-- 环境信息 -->
        <div class="env-info">
          <div class="env-line">空气质量: {{ weatherData?.air_quality || "中" }}</div>
          <div class="env-line">湿度: {{ weatherData?.humidity || 52 }}%</div>
          <div class="env-line">能见度: {{ weatherData?.visibility || 30 }}km</div>
        </div>
      </div>

      <!-- 底部数据行 -->
      <div class="bottom-row">
        <div class="data-item">
          <div class="data-label">车外PM2.5</div>
          <div class="data-value orange">{{ weatherData?.pm25_outdoor || "105" }}</div>
        </div>

        <div class="data-item">
          <div class="data-label">车内PM2.5</div>
          <div class="data-value green">{{ weatherData?.pm25_indoor || "14" }}</div>
        </div>

        <div class="data-item">
          <div class="data-label">车外负氧离子</div>
          <div class="data-value dark-green">{{ weatherData?.negative_ions || "628" }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { WeatherData } from '../../composables/useWeatherData'

interface Props {
  weatherData: WeatherData | null
  loading: boolean
}

defineProps<Props>()

defineEmits<{
  refresh: []
}>()

// 格式化发布日期
const formatDate = (publishTime?: string) => {
  if (!publishTime) return "2025-06-09"
  return publishTime.split(' ')[0]
}

// 格式化发布时间
const formatTime = (publishTime?: string) => {
  if (!publishTime) return "15:07:03"
  return publishTime.split(' ')[1]
}
</script>

<style scoped>
/* 主容器 */
.weather-card {
  @apply absolute h-[183px] top-[409px] left-[23px] w-[345px];
  @apply bg-white/60 backdrop-blur-sm rounded-2xl;
  @apply relative;
  font-family: 'HarmonyOS_Sans-Regular', Helvetica;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

/* 加载状态 */
.loading-container {
  @apply flex flex-col items-center justify-center h-full space-y-2;
}

.loading-spinner {
  @apply w-4 h-4 border-2 border-green-600 border-t-transparent rounded-full animate-spin;
}

.loading-text {
  @apply text-green-600 text-sm font-normal;
}

/* 天气内容 */
.weather-content {
  @apply h-full relative;
  padding: 16px 20px 20px 20px;
}

/* 刷新按钮 */
.refresh-btn {
  @apply absolute top-2 right-3 text-orange-400 text-[8px] font-normal;
  @apply hover:text-orange-500 transition-colors cursor-pointer;
  @apply bg-transparent border-none z-10;
  @apply flex items-center gap-0.5;
}

.refresh-icon {
  @apply w-2.5 h-2.5;
}

/* 顶部行 */
.top-row {
  @apply grid grid-cols-3 gap-4 items-start;
  margin-bottom: 32px;
}

/* 城市信息 */
.city-info {
  @apply flex flex-col;
}

.city-name {
  @apply text-green-700 font-bold leading-tight;
  font-size: 26px;
  margin-bottom: 4px;
}

.publish-info {
  @apply text-green-600 font-normal leading-tight;
  font-size: 8px;
}

/* 温度 */
.temperature {
  @apply flex items-baseline justify-center;
}

.temp-number {
  @apply text-green-700 font-bold leading-none;
  font-size: 64px;
}

.temp-symbol {
  @apply text-green-700 font-bold;
  font-size: 28px;
  margin-left: 2px;
}

/* 环境信息 */
.env-info {
  @apply flex flex-col text-right;
}

.env-line {
  @apply text-green-600 font-normal leading-tight;
  font-size: 8px;
  margin-bottom: 2px;
}

/* 底部数据行 */
.bottom-row {
  @apply grid grid-cols-3 gap-4 items-end;
}

.data-item {
  @apply flex flex-col text-center;
}

.data-label {
  @apply text-green-600 font-normal leading-tight;
  font-size: 8px;
  margin-bottom: 8px;
}

.data-value {
  @apply font-bold leading-none;
  font-size: 42px;
}

/* 数据颜色 */
.data-value.orange {
  @apply text-orange-500;
}

.data-value.green {
  @apply text-emerald-500;
}

.data-value.dark-green {
  @apply text-green-700;
}

/* 响应式 */
@media (max-width: 400px) {
  .top-row {
    @apply flex-col items-center text-center space-y-2;
  }

  .bottom-row {
    @apply flex-col space-y-2;
  }

  .temp-number {
    @apply text-[36px];
  }

  .data-value {
    @apply text-[24px];
  }

  .env-info {
    @apply text-center;
  }
}

/* 动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.weather-content {
  animation: fadeIn 0.4s ease-out;
}
</style>
