<template>
  <div class="weather-card">
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <div class="loading-spinner"></div>
      <div class="loading-text">加载中...</div>
    </div>

    <!-- 天气内容 -->
    <div v-else class="weather-content">
      <!-- 头部区域：城市、温度、刷新 -->
      <div class="header-row">
        <div class="city-section">
          <h1 class="city-title">{{ weatherData?.city || "深圳市" }}</h1>
          <div class="date-info">
            <span class="date">{{ formatDate(weatherData?.publish_time) }}</span>
            <span class="time">{{ formatTime(weatherData?.publish_time) }}</span>
            <span class="publish">发布</span>
          </div>
        </div>

        <div class="temperature-section">
          <span class="temp-value">{{ weatherData?.temperature || 33 }}</span>
          <span class="temp-unit">°C</span>
        </div>

        <div class="action-section">
          <button @click="$emit('refresh')" class="refresh-button">
            刷新
          </button>
          <div class="air-quality">
            <div class="air-row">
              <span class="label">空气质量:</span>
              <span class="value">{{ weatherData?.air_quality || "中" }}</span>
            </div>
            <div class="air-row">
              <span class="label">湿度:</span>
              <span class="value">{{ weatherData?.humidity || 52 }}%</span>
            </div>
            <div class="air-row">
              <span class="label">能见度:</span>
              <span class="value">{{ weatherData?.visibility || 30 }}km</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 数据指标区域 -->
      <div class="metrics-row">
        <div class="metric-box pm-outdoor">
          <div class="metric-title">车外PM2.5</div>
          <div class="metric-number">{{ weatherData?.pm25_outdoor || "105" }}</div>
        </div>

        <div class="metric-box pm-indoor">
          <div class="metric-title">车内PM2.5</div>
          <div class="metric-number">{{ weatherData?.pm25_indoor || "14" }}</div>
        </div>

        <div class="metric-box negative-ions">
          <div class="metric-title">车外负氧离子</div>
          <div class="metric-number">{{ weatherData?.negative_ions || "628" }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { WeatherData } from '../../composables/useWeatherData'

interface Props {
  weatherData: WeatherData | null
  loading: boolean
}

defineProps<Props>()

defineEmits<{
  refresh: []
}>()

// 格式化发布日期
const formatDate = (publishTime?: string) => {
  if (!publishTime) return "2025-06-09"
  return publishTime.split(' ')[0]
}

// 格式化发布时间
const formatTime = (publishTime?: string) => {
  if (!publishTime) return "15:07:03"
  return publishTime.split(' ')[1]
}
</script>

<style scoped>
/* 主容器 */
.weather-card {
  @apply absolute h-[183px] top-[409px] left-[23px] w-[345px];
  @apply bg-white/60 backdrop-blur-sm rounded-2xl;
  @apply px-4 py-3;
  font-family: 'HarmonyOS_Sans-Regular', Helvetica;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

/* 加载状态 */
.loading-container {
  @apply flex flex-col items-center justify-center h-full space-y-2;
}

.loading-spinner {
  @apply w-4 h-4 border-2 border-green-600 border-t-transparent rounded-full animate-spin;
}

.loading-text {
  @apply text-green-600 text-sm font-normal;
}

/* 天气内容 */
.weather-content {
  @apply h-full flex flex-col justify-between;
}

/* 头部区域 */
.header-row {
  @apply grid grid-cols-3 gap-3 items-start mb-4;
}

/* 城市区域 */
.city-section {
  @apply flex flex-col;
}

.city-title {
  @apply text-green-700 text-[24px] font-bold leading-tight mb-1;
}

.date-info {
  @apply flex flex-col text-green-600 text-[9px] font-normal leading-tight;
}

.date, .time {
  @apply block;
}

.publish {
  @apply block mt-1;
}

/* 温度区域 */
.temperature-section {
  @apply flex items-baseline justify-center;
}

.temp-value {
  @apply text-green-700 text-[48px] font-bold leading-none;
}

.temp-unit {
  @apply text-green-700 text-[24px] font-bold ml-1;
}

/* 右侧操作区域 */
.action-section {
  @apply flex flex-col items-end;
}

.refresh-button {
  @apply text-orange-400 text-[9px] font-normal mb-2;
  @apply hover:text-orange-500 transition-colors cursor-pointer;
  @apply bg-transparent border-none p-1;
  @apply self-end;
}

.air-quality {
  @apply flex flex-col items-end text-right space-y-1;
}

.air-row {
  @apply flex items-center space-x-1;
}

.label {
  @apply text-green-600 text-[9px] font-normal;
}

.value {
  @apply text-green-600 text-[9px] font-medium;
}

/* 指标区域 */
.metrics-row {
  @apply grid grid-cols-3 gap-2;
}

.metric-box {
  @apply flex flex-col items-center text-center;
  @apply bg-white/30 rounded-lg p-2;
  @apply transition-all duration-200;
}

.metric-box:hover {
  @apply bg-white/40 transform scale-105;
}

.metric-title {
  @apply text-green-600 text-[9px] font-normal leading-tight mb-1;
}

.metric-number {
  @apply text-[36px] font-bold leading-none;
}

/* 指标颜色 */
.pm-outdoor .metric-number {
  @apply text-orange-500;
}

.pm-indoor .metric-number {
  @apply text-emerald-500;
}

.negative-ions .metric-number {
  @apply text-green-700;
}

/* 指标边框 */
.pm-outdoor {
  @apply border border-orange-200/50;
}

.pm-indoor {
  @apply border border-emerald-200/50;
}

.negative-ions {
  @apply border border-green-200/50;
}

/* 响应式 */
@media (max-width: 400px) {
  .header-row {
    @apply grid-cols-1 gap-2 text-center;
  }

  .metrics-row {
    @apply grid-cols-1 gap-2;
  }

  .temp-value {
    @apply text-[36px];
  }

  .metric-number {
    @apply text-[24px];
  }

  .action-section {
    @apply items-center;
  }
}

/* 动画 */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.weather-content {
  animation: slideIn 0.4s ease-out;
}

.metric-box {
  animation: slideIn 0.4s ease-out;
}

.metric-box:nth-child(1) {
  animation-delay: 0.1s;
}

.metric-box:nth-child(2) {
  animation-delay: 0.2s;
}

.metric-box:nth-child(3) {
  animation-delay: 0.3s;
}
</style>
