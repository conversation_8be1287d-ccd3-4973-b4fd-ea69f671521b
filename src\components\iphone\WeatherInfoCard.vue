<template>
  <div class="weather-card">
    <!-- 刷新按钮 -->
    <button @click="$emit('refresh')" class="refresh-btn">
      刷新
    </button>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <div class="loading-spinner"></div>
      <div class="loading-text">加载中...</div>
    </div>

    <!-- 天气内容 -->
    <div v-else class="weather-container">
      <!-- 顶部信息区 -->
      <div class="top-section">
        <!-- 左侧：城市和发布时间 -->
        <div class="city-info">
          <h2 class="city-name">{{ weatherData?.city || "深圳市" }}</h2>
          <div class="publish-time">
            {{ formatDate(weatherData?.publish_time) }}
          </div>
          <div class="publish-time">
            {{ formatTime(weatherData?.publish_time) }}
          </div>
          <div class="publish-label">发布</div>
        </div>

        <!-- 中间：温度 -->
        <div class="temperature-display">
          <div class="temperature-value">{{ weatherData?.temperature || 33 }}°C</div>
        </div>

        <!-- 右侧：环境信息 -->
        <div class="environment-info">
          <div class="env-item">
            <span class="env-label">空气质量:</span>
            <span class="env-value quality">{{ weatherData?.air_quality || "中" }}</span>
          </div>
          <div class="env-item">
            <span class="env-label">湿度:</span>
            <span class="env-value">{{ weatherData?.humidity || 52 }}%</span>
          </div>
          <div class="env-item">
            <span class="env-label">能见度:</span>
            <span class="env-value">{{ weatherData?.visibility || 30 }}km</span>
          </div>
        </div>
      </div>

      <!-- 底部数据区 -->
      <div class="data-section">
        <div class="data-item outdoor">
          <div class="data-label">车外PM2.5</div>
          <div class="data-value outdoor-color">{{ weatherData?.pm25_outdoor || "105" }}</div>
        </div>

        <div class="data-item indoor">
          <div class="data-label">车内PM2.5</div>
          <div class="data-value indoor-color">{{ weatherData?.pm25_indoor || "14" }}</div>
        </div>

        <div class="data-item ions">
          <div class="data-label">车外负氧离子</div>
          <div class="data-value ions-color">{{ weatherData?.negative_ions || "628" }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { WeatherData } from '../../composables/useWeatherData'

interface Props {
  weatherData: WeatherData | null
  loading: boolean
}

defineProps<Props>()

defineEmits<{
  refresh: []
}>()

// 格式化发布日期
const formatDate = (publishTime?: string) => {
  if (!publishTime) return "2025-06-09"
  return publishTime.split(' ')[0]
}

// 格式化发布时间
const formatTime = (publishTime?: string) => {
  if (!publishTime) return "15:07:03"
  return publishTime.split(' ')[1]
}
</script>

<style scoped>
/* 主容器 */
.weather-card {
  @apply absolute h-[183px] top-[409px] left-[23px] w-[345px];
  @apply bg-white/60 backdrop-blur-sm rounded-2xl overflow-hidden;
  @apply relative px-5 py-4;
  font-family: 'HarmonyOS_Sans-Regular', Helvetica;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

/* 刷新按钮 */
.refresh-btn {
  @apply absolute top-3 right-3 text-orange-400 text-[9px] font-normal;
  @apply hover:text-orange-500 transition-colors cursor-pointer;
  @apply bg-transparent border-none z-10;
}

/* 加载状态 */
.loading-container {
  @apply flex flex-col items-center justify-center h-full space-y-2;
}

.loading-spinner {
  @apply w-4 h-4 border-2 border-green-600 border-t-transparent rounded-full animate-spin;
}

.loading-text {
  @apply text-green-600 text-sm font-normal;
}

/* 天气内容容器 */
.weather-container {
  @apply h-full flex flex-col justify-between;
}

/* 顶部信息区 */
.top-section {
  @apply grid grid-cols-3 gap-4 items-start mb-6;
}

/* 城市信息 */
.city-info {
  @apply flex flex-col justify-start;
}

.city-name {
  @apply text-green-700 text-[24px] font-bold leading-tight mb-1;
}

.publish-time {
  @apply text-green-600 text-[9px] font-normal leading-tight;
}

.publish-label {
  @apply text-green-600 text-[9px] font-normal mt-1;
}

/* 温度显示 */
.temperature-display {
  @apply flex justify-center items-center;
}

.temperature-value {
  @apply text-green-700 text-[48px] font-bold leading-none;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 环境信息 */
.environment-info {
  @apply flex flex-col items-end text-right space-y-1;
}

.env-item {
  @apply flex items-center space-x-1;
}

.env-label {
  @apply text-green-600 text-[9px] font-normal;
}

.env-value {
  @apply text-green-600 text-[9px] font-medium;
}

.env-value.quality {
  @apply font-bold;
}

/* 底部数据区 */
.data-section {
  @apply grid grid-cols-3 gap-3;
}

.data-item {
  @apply flex flex-col items-center text-center;
  @apply bg-gradient-to-b from-white/40 to-white/20;
  @apply rounded-xl p-3 border border-white/30;
  @apply transition-all duration-300 ease-in-out;
  @apply hover:transform hover:scale-105 hover:shadow-lg;
  backdrop-filter: blur(10px);
}

.data-item:hover {
  @apply bg-gradient-to-b from-white/60 to-white/40;
}

.data-label {
  @apply text-green-600 text-[9px] font-normal leading-tight mb-2;
}

.data-value {
  @apply text-[36px] font-bold leading-none;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 数据颜色 */
.outdoor-color {
  @apply text-orange-500;
}

.indoor-color {
  @apply text-emerald-500;
}

.ions-color {
  @apply text-green-700;
}

/* 特殊效果 */
.data-item.outdoor {
  @apply border-orange-200/50;
}

.data-item.indoor {
  @apply border-emerald-200/50;
}

.data-item.ions {
  @apply border-green-200/50;
}

/* 响应式调整 */
@media (max-width: 400px) {
  .top-section {
    @apply grid-cols-1 gap-2 text-center mb-4;
  }

  .data-section {
    @apply grid-cols-1 gap-2;
  }

  .temperature-value {
    @apply text-[36px];
  }

  .data-value {
    @apply text-[24px];
  }

  .environment-info {
    @apply items-center text-center;
  }
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.weather-container {
  animation: fadeInUp 0.6s ease-out;
}

.data-item {
  animation: fadeInUp 0.6s ease-out;
}

.data-item:nth-child(1) {
  animation-delay: 0.1s;
}

.data-item:nth-child(2) {
  animation-delay: 0.2s;
}

.data-item:nth-child(3) {
  animation-delay: 0.3s;
}
</style>
