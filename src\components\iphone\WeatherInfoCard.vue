<template>
  <div class="weather-card">
    <div class="card-content">
      <!-- 刷新按钮 -->
      <button @click="$emit('refresh')" class="refresh-button">
        刷新
      </button>

      <!-- 天气数据布局 -->
      <div class="weather-layout">
        <div v-if="loading" class="loading-state">
          <div class="loading-text">加载中...</div>
        </div>
        <div v-else class="weather-content">
          <!-- 顶部行：3列网格 -->
          <div class="top-grid">
            <!-- 第1列：城市信息 -->
            <div class="city-info">
              <div class="city-name">
                {{ weatherData?.city || "深圳市" }}
              </div>
              <div class="publish-time">
                {{ weatherData?.publish_time || "2025-06-09 15:07:03" }}发布
              </div>
            </div>

            <!-- 第2列：温度 -->
            <div class="temperature-section">
              <div class="temperature">
                {{ weatherData?.temperature || 33 }}°C
              </div>
            </div>

            <!-- 第3列：空气质量 -->
            <div class="air-quality-info">
              <div>空气质量: {{ weatherData?.air_quality || "中" }}</div>
              <div>湿度: {{ weatherData?.humidity || 52 }}%</div>
              <div>能见度: {{ weatherData?.visibility || 30 }}km</div>
            </div>
          </div>

          <!-- 底部行：3列网格 -->
          <div class="bottom-grid">
            <!-- 车外PM2.5 -->
            <div class="pm-outdoor">
              <div class="pm-label">车外PM2.5</div>
              <div class="pm-value outdoor-value">
                {{ weatherData?.pm25_outdoor || "105" }}
              </div>
            </div>

            <!-- 车内PM2.5 -->
            <div class="pm-indoor">
              <div class="pm-label">车内PM2.5</div>
              <div class="pm-value indoor-value">
                {{ weatherData?.pm25_indoor || "14" }}
              </div>
            </div>

            <!-- 车外负氧离子 -->
            <div class="negative-ions">
              <div class="pm-label">车外负氧离子</div>
              <div class="pm-value ions-value">
                {{ weatherData?.negative_ions || "628" }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { WeatherData } from '../../composables/useWeatherData'

interface Props {
  weatherData: WeatherData | null
  loading: boolean
}

defineProps<Props>()

defineEmits<{
  refresh: []
}>()
</script>

<style scoped>
.weather-card {
  @apply absolute h-[183px] top-[409px] left-[23px] w-[345px] bg-white/60 backdrop-blur-sm rounded-2xl overflow-hidden;
}

.card-content {
  @apply relative h-full px-4 py-4;
}

.refresh-button {
  @apply absolute top-3 right-3 text-orange-400 text-[9px];
  font-family: 'HarmonyOS_Sans-Regular', Helvetica;
}

.weather-layout {
  @apply flex flex-col h-full justify-center px-6 py-4;
}

.loading-state {
  @apply flex items-center justify-center h-full;
}

.loading-text {
  @apply text-green-600 text-sm animate-pulse;
}

.weather-content {
  @apply space-y-8;
}

.top-grid {
  @apply grid grid-cols-3 gap-4 items-start;
}

.city-info {
  @apply flex flex-col justify-start;
}

.city-name {
  @apply text-green-700 text-[24px] font-bold leading-tight;
  font-family: 'HarmonyOS_Sans-Regular', Helvetica;
}

.publish-time {
  @apply text-green-600 text-[9px] font-normal leading-tight mt-1;
  font-family: 'HarmonyOS_Sans-Regular', Helvetica;
}

.temperature-section {
  @apply flex justify-center items-start;
}

.temperature {
  @apply text-green-700 text-[48px] font-bold leading-none;
  font-family: 'HarmonyOS_Sans-Regular', Helvetica;
}

.air-quality-info {
  @apply flex flex-col items-end text-right text-green-600 text-[9px] font-normal leading-tight;
  font-family: 'HarmonyOS_Sans-Regular', Helvetica;
}

.bottom-grid {
  @apply grid grid-cols-3 gap-4 items-end;
}

.pm-outdoor,
.pm-indoor,
.negative-ions {
  @apply flex flex-col items-center;
}

.pm-label {
  @apply text-green-600 text-[9px] font-normal mb-2;
  font-family: 'HarmonyOS_Sans-Regular', Helvetica;
}

.pm-value {
  @apply text-[36px] font-bold leading-none;
  font-family: 'HarmonyOS_Sans-Regular', Helvetica;
}

.outdoor-value {
  @apply text-orange-500;
}

.indoor-value {
  @apply text-emerald-500;
}

.ions-value {
  @apply text-green-700;
}
</style>
