<template>
  <div class="weather-card">
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <div class="loading-spinner"></div>
      <div class="loading-text">加载中...</div>
    </div>

    <!-- 天气内容 -->
    <div v-else class="weather-content">
      <!-- 刷新按钮 -->
      <button @click="$emit('refresh')" class="refresh-btn">
        <svg class="refresh-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M4 4V9H4.58152M4.58152 9C5.24618 7.35652 6.43101 5.9604 7.96 5.05C9.48899 4.1396 11.2943 3.75 13.1152 3.94148C14.9361 4.13295 16.6618 4.89479 18.0434 6.12C19.4251 7.34521 20.3898 8.97953 20.8 10.78M4.58152 9H9M20 20V15H19.4185M19.4185 15C18.7538 16.6435 17.569 18.0396 16.04 18.95C14.511 19.8604 12.7057 20.25 10.8848 20.0585C9.06393 19.867 7.33818 19.1052 5.95655 17.88C4.57493 16.6548 3.61015 15.0205 3.2 13.22M19.4185 15H15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
        刷新
      </button>

      <!-- 上盒子：基础信息 -->
      <div class="top-box">
        <div class="city-section">
          <div class="city-name">{{ weatherData?.city || "深圳市" }}</div>
          <div class="publish-info">
            {{ formatDate(weatherData?.publish_time) }} {{ formatTime(weatherData?.publish_time) }}发布
          </div>
        </div>

        <div class="temp-section">
          <span class="temp-number">{{ weatherData?.temperature || 33 }}</span>
          <span class="temp-symbol">°C</span>
        </div>

        <div class="env-section">
          <div class="env-line">空气质量: {{ weatherData?.air_quality || "中" }}</div>
          <div class="env-line">湿度: {{ weatherData?.humidity || 52 }}%</div>
          <div class="env-line">能见度: {{ weatherData?.visibility || 30 }}km</div>
        </div>
      </div>

      <!-- 下盒子：数据指标 -->
      <div class="bottom-box">
        <div class="data-section">
          <div class="data-label">车外PM2.5</div>
          <div class="data-value orange">{{ weatherData?.pm25_outdoor || "105" }}</div>
        </div>

        <div class="data-section">
          <div class="data-label">车内PM2.5</div>
          <div class="data-value green">{{ weatherData?.pm25_indoor || "14" }}</div>
        </div>

        <div class="data-section">
          <div class="data-label">车外负氧离子</div>
          <div class="data-value dark-green">{{ weatherData?.negative_ions || "628" }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { WeatherData } from '../../composables/useWeatherData'

interface Props {
  weatherData: WeatherData | null
  loading: boolean
}

defineProps<Props>()

defineEmits<{
  refresh: []
}>()

// 格式化发布日期
const formatDate = (publishTime?: string) => {
  if (!publishTime) return "2025-06-09"
  return publishTime.split(' ')[0]
}

// 格式化发布时间
const formatTime = (publishTime?: string) => {
  if (!publishTime) return "15:07:03"
  return publishTime.split(' ')[1]
}
</script>

<style scoped>
/* 主容器 */
.weather-card {
  position: absolute;
  height: 183px;
  top: 409px;
  left: 23px;
  width: 345px;
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  font-family: 'HarmonyOS_Sans-Regular', Helvetica;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  gap: 8px;
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid #16a34a;
  border-top: 2px solid transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  color: #16a34a;
  font-size: 14px;
}

/* 天气内容 */
.weather-content {
  height: 100%;
  position: relative;
  padding: 16px 20px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

/* 刷新按钮 */
.refresh-btn {
  position: absolute;
  top: 8px;
  right: 12px;
  color: #fb923c;
  font-size: 8px;
  background: transparent;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 2px;
  z-index: 10;
}

.refresh-btn:hover {
  color: #f97316;
}

.refresh-icon {
  width: 10px;
  height: 10px;
}

/* 上盒子 */
.top-box {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 16px;
  height: 80px;
}

/* 城市区域 */
.city-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: 100%;
}

.city-name {
  color: #15803d;
  font-size: 26px;
  font-weight: bold;
  line-height: 1.2;
  margin-bottom: 4px;
}

.publish-info {
  color: #16a34a;
  font-size: 8px;
  line-height: 1.2;
}

/* 温度区域 */
.temp-section {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.temp-number {
  color: #15803d;
  font-size: 64px;
  font-weight: bold;
  line-height: 1;
}

.temp-symbol {
  color: #15803d;
  font-size: 28px;
  font-weight: bold;
  margin-left: 2px;
  align-self: flex-start;
  margin-top: 8px;
}

/* 环境区域 */
.env-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  text-align: right;
  height: 100%;
}

.env-line {
  color: #16a34a;
  font-size: 8px;
  line-height: 1.4;
  margin-bottom: 2px;
}

/* 下盒子 */
.bottom-box {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  gap: 16px;
  height: 60px;
}

/* 数据区域 */
.data-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-end;
  height: 100%;
}

.data-label {
  color: #16a34a;
  font-size: 8px;
  line-height: 1.2;
  margin-bottom: 8px;
  text-align: center;
}

.data-value {
  font-size: 42px;
  font-weight: bold;
  line-height: 1;
  text-align: center;
}

/* 数据颜色 */
.data-value.orange {
  color: #f97316;
}

.data-value.green {
  color: #10b981;
}

.data-value.dark-green {
  color: #15803d;
}

/* 动画 */
@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.weather-content {
  animation: fadeIn 0.4s ease-out;
}
</style>
