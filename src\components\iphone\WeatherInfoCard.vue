<template>
  <div class="weather-card">
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <div class="loading-spinner"></div>
      <div class="loading-text">加载中...</div>
    </div>

    <!-- 天气内容 -->
    <div v-else class="weather-content">
      <!-- 刷新按钮 -->
      <button @click="$emit('refresh')" class="refresh-btn">
        刷新
      </button>

      <!-- 顶部信息行 -->
      <div class="top-row">
        <!-- 城市和时间 -->
        <div class="city-info">
          <div class="city-name">{{ weatherData?.city || "深圳市" }}</div>
          <div class="publish-info">
            <div class="date">{{ formatDate(weatherData?.publish_time) }}</div>
            <div class="time">{{ formatTime(weatherData?.publish_time) }}</div>
            <div class="publish-label">发布</div>
          </div>
        </div>

        <!-- 温度 -->
        <div class="temperature">
          <span class="temp-number">{{ weatherData?.temperature || 33 }}</span>
          <span class="temp-symbol">°C</span>
        </div>

        <!-- 环境信息 -->
        <div class="env-info">
          <div class="env-line">空气质量: {{ weatherData?.air_quality || "中" }}</div>
          <div class="env-line">湿度: {{ weatherData?.humidity || 52 }}%</div>
          <div class="env-line">能见度: {{ weatherData?.visibility || 30 }}km</div>
        </div>
      </div>

      <!-- 底部数据行 -->
      <div class="bottom-row">
        <div class="data-item">
          <div class="data-label">车外PM2.5</div>
          <div class="data-value orange">{{ weatherData?.pm25_outdoor || "105" }}</div>
        </div>

        <div class="data-item">
          <div class="data-label">车内PM2.5</div>
          <div class="data-value green">{{ weatherData?.pm25_indoor || "14" }}</div>
        </div>

        <div class="data-item">
          <div class="data-label">车外负氧离子</div>
          <div class="data-value dark-green">{{ weatherData?.negative_ions || "628" }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { WeatherData } from '../../composables/useWeatherData'

interface Props {
  weatherData: WeatherData | null
  loading: boolean
}

defineProps<Props>()

defineEmits<{
  refresh: []
}>()

// 格式化发布日期
const formatDate = (publishTime?: string) => {
  if (!publishTime) return "2025-06-09"
  return publishTime.split(' ')[0]
}

// 格式化发布时间
const formatTime = (publishTime?: string) => {
  if (!publishTime) return "15:07:03"
  return publishTime.split(' ')[1]
}
</script>

<style scoped>
/* 主容器 */
.weather-card {
  @apply absolute h-[183px] top-[409px] left-[23px] w-[345px];
  @apply bg-white/60 backdrop-blur-sm rounded-2xl;
  @apply relative;
  font-family: 'HarmonyOS_Sans-Regular', Helvetica;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

/* 加载状态 */
.loading-container {
  @apply flex flex-col items-center justify-center h-full space-y-2;
}

.loading-spinner {
  @apply w-4 h-4 border-2 border-green-600 border-t-transparent rounded-full animate-spin;
}

.loading-text {
  @apply text-green-600 text-sm font-normal;
}

/* 天气内容 */
.weather-content {
  @apply h-full p-4 flex flex-col justify-between;
}

/* 刷新按钮 */
.refresh-btn {
  @apply absolute top-3 right-3 text-orange-400 text-[9px] font-normal;
  @apply hover:text-orange-500 transition-colors cursor-pointer;
  @apply bg-transparent border-none z-10;
}

/* 顶部行 */
.top-row {
  @apply flex items-start justify-between mb-6;
}

/* 城市信息 */
.city-info {
  @apply flex-1;
}

.city-name {
  @apply text-green-700 text-[24px] font-bold leading-tight mb-1;
}

.publish-info {
  @apply text-green-600 text-[9px] font-normal leading-tight;
}

.date, .time, .publish-label {
  @apply block;
}

/* 温度 */
.temperature {
  @apply flex items-baseline justify-center flex-1;
}

.temp-number {
  @apply text-green-700 text-[48px] font-bold leading-none;
}

.temp-symbol {
  @apply text-green-700 text-[24px] font-bold ml-1;
}

/* 环境信息 */
.env-info {
  @apply flex-1 text-right;
}

.env-line {
  @apply text-green-600 text-[9px] font-normal leading-tight mb-1;
}

/* 底部数据行 */
.bottom-row {
  @apply flex justify-between items-end;
}

.data-item {
  @apply flex-1 text-center;
}

.data-label {
  @apply text-green-600 text-[9px] font-normal leading-tight mb-2;
}

.data-value {
  @apply text-[36px] font-bold leading-none;
}

/* 数据颜色 */
.data-value.orange {
  @apply text-orange-500;
}

.data-value.green {
  @apply text-emerald-500;
}

.data-value.dark-green {
  @apply text-green-700;
}

/* 响应式 */
@media (max-width: 400px) {
  .top-row {
    @apply flex-col items-center text-center space-y-2;
  }

  .bottom-row {
    @apply flex-col space-y-2;
  }

  .temp-number {
    @apply text-[36px];
  }

  .data-value {
    @apply text-[24px];
  }

  .env-info {
    @apply text-center;
  }
}

/* 动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.weather-content {
  animation: fadeIn 0.4s ease-out;
}
</style>
