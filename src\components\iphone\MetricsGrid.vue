<template>
  <div class="metrics-container">
    <div 
      v-for="(metric, index) in metrics" 
      :key="index" 
      :class="['metric-item', metric.width]"
    >
      <div class="metric-content">
        <div class="metric-value">{{ metric.value }}</div>
        <div class="metric-label">{{ metric.label }}</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Metric {
  value: string
  label: string
  width: string
}

interface Props {
  metrics: Metric[]
}

defineProps<Props>()
</script>

<style scoped>
.metrics-container {
  @apply absolute w-[298px] h-[45px] top-80 left-[42px] flex justify-between;
}

.metric-item {
  @apply h-[45px];
}

.metric-content {
  @apply relative h-full;
}

.metric-value {
  @apply absolute top-0 left-1.5 font-normal text-[#494949] text-[28px] tracking-[0] leading-[normal] whitespace-nowrap;
  font-family: 'HarmonyOS_Sans-Regular', Helvetica;
}

.metric-label {
  @apply absolute top-[33px] left-0 font-normal text-[#909090] text-[10px] tracking-[0] leading-[normal] whitespace-nowrap;
  font-family: 'HarmonyOS_Sans-Regular', Helvetica;
}
</style>
