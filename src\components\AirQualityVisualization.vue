<template>
  <div class="air-quality-container">
    <!-- 空气质量指示器 -->
    <div class="air-quality-indicator">
      <div class="indicator-content">
        <div class="air-status">{{ airQuality.status }}</div>
        <div class="air-level">{{ airQuality.level }}</div>
      </div>
      <div class="air-description">{{ airQuality.description }}</div>
    </div>

    <!-- 空气质量标志 -->
    <img
      class="air-quality-logo"
      alt="Air quality logo"
      src="https://c.animaapp.com/mdqqarhondzElL/img/group-13.png"
    />

    <!-- 健康守护天数 -->
    <div class="protection-days">
      <span class="protection-text">已为您健康守护 </span>
      <span class="days-number">{{ protectionDays }}</span>
      <span class="protection-text"> 天</span>
    </div>

    <!-- 清洗提醒设置 -->
    <div class="cleaning-reminder">
      <span class="reminder-text">设置 </span>
      <span class="reminder-link">清洗提醒</span>
    </div>
  </div>
</template>

<script setup lang="ts">
interface AirQuality {
  level: number
  status: string
  description: string
}

interface Props {
  airQuality: AirQuality
  protectionDays: number
}

defineProps<Props>()
</script>

<style scoped>
.air-quality-container {
  @apply relative;
}

.air-quality-indicator {
  @apply absolute w-[75px] h-24 top-[49px] left-[166px];
}

.indicator-content {
  @apply absolute w-[69px] h-[87px] top-[9px] left-0;
}

.air-status {
  @apply top-0 left-1 text-[#454545] text-xl whitespace-nowrap absolute font-normal tracking-[0] leading-[normal];
  font-family: 'HarmonyOS_Sans-Regular', Helvetica;
}

.air-level {
  @apply top-[17px] left-0 text-[#1b705f] text-6xl absolute font-normal tracking-[0] leading-[normal] whitespace-nowrap;
  font-family: 'HarmonyOS_Sans-Regular', Helvetica;
}

.air-description {
  @apply top-0 left-2.5 text-[#454545] text-[6px] whitespace-nowrap absolute font-normal tracking-[0] leading-[normal];
  font-family: 'HarmonyOS_Sans-Regular', Helvetica;
}

.air-quality-logo {
  @apply absolute w-[177px] h-[90px] top-[58px] left-[105px];
}

.protection-days {
  @apply top-64 left-[109px] text-transparent text-[15px] absolute font-normal tracking-[0] leading-[normal];
  font-family: 'HarmonyOS_Sans-Regular', Helvetica;
}

.protection-text {
  @apply text-[#5b5b5b];
}

.days-number {
  @apply text-[#5b5b5b] text-[19px];
}

.cleaning-reminder {
  @apply top-[282px] left-[149px] text-transparent text-[15px] whitespace-nowrap absolute font-normal tracking-[0] leading-[normal];
  font-family: 'HarmonyOS_Sans-Regular', Helvetica;
}

.reminder-text {
  @apply text-[#5b5b5b];
}

.reminder-link {
  @apply text-[#ff8800];
}
</style>
