<template>
  <div class="bg-gray-50 flex flex-row justify-center w-full min-h-screen">
    <div class="bg-gray-50 w-[393px] h-[852px] relative overflow-hidden">
      <!-- Status bar -->
      <div class="absolute w-[375px] h-11 top-0 left-[11px] flex items-center justify-between px-4 text-black">
        <span class="text-[17px] font-semibold">9:41</span>
        <div class="flex items-center gap-1">
          <div class="flex gap-1">
            <div class="w-1 h-1 bg-black rounded-full"></div>
            <div class="w-1 h-1 bg-black rounded-full"></div>
            <div class="w-1 h-1 bg-black rounded-full"></div>
            <div class="w-1 h-1 bg-black rounded-full"></div>
          </div>
          <svg width="24" height="17" viewBox="0 0 24 17" fill="none">
            <path d="M2 4.5C2 3.11929 3.11929 2 4.5 2H19.5C20.8807 2 22 3.11929 22 4.5V12.5C22 13.8807 20.8807 15 19.5 15H4.5C3.11929 15 2 13.8807 2 12.5V4.5Z" stroke="black" stroke-width="1"/>
            <path d="M23 6V11C23.5523 11 24 10.5523 24 10V7C24 6.44772 23.5523 6 23 6Z" fill="black"/>
          </svg>
        </div>
      </div>

      <div class="absolute w-[393px] h-[783px] top-[69px] left-0">
        <!-- Main background with beautiful gradient -->
        <div class="absolute w-[393px] h-[400px] top-0 left-0 overflow-hidden">
          <!-- Large gradient circle -->
          <div class="absolute w-[350px] h-[350px] top-[-20px] left-[21px] rounded-full"
               style="background: linear-gradient(135deg,
                 #fef3c7 0%,
                 #fde68a 15%,
                 #fed7aa 30%,
                 #fecaca 45%,
                 #f3e8ff 60%,
                 #ddd6fe 75%,
                 #a7f3d0 90%,
                 #86efac 100%);
                 opacity: 0.9;">
          </div>

          <!-- Inner gradient overlay for depth -->
          <div class="absolute w-[280px] h-[280px] top-[15px] left-[56px] rounded-full"
               style="background: radial-gradient(circle at 30% 30%,
                 rgba(255, 255, 255, 0.4) 0%,
                 rgba(255, 255, 255, 0.2) 30%,
                 rgba(167, 243, 208, 0.3) 60%,
                 transparent 100%);">
          </div>

          <!-- Floating particles -->
          <div class="absolute w-3 h-3 bg-emerald-400 rounded-full top-[80px] left-[80px] animate-pulse opacity-70 shadow-lg"></div>
          <div class="absolute w-2 h-2 bg-blue-400 rounded-full top-[120px] left-[160px] animate-pulse opacity-60 shadow-md" style="animation-delay: 0.3s;"></div>
          <div class="absolute w-2.5 h-2.5 bg-green-500 rounded-full top-[100px] left-[280px] animate-pulse opacity-50 shadow-md" style="animation-delay: 0.7s;"></div>
          <div class="absolute w-2 h-2 bg-orange-400 rounded-full top-[140px] left-[100px] animate-pulse opacity-60 shadow-md" style="animation-delay: 0.5s;"></div>
          <div class="absolute w-3 h-3 bg-blue-500 rounded-full top-[160px] left-[250px] animate-pulse opacity-50 shadow-lg" style="animation-delay: 1s;"></div>
          <div class="absolute w-1.5 h-1.5 bg-green-400 rounded-full top-[70px] left-[320px] animate-pulse opacity-70 shadow-sm" style="animation-delay: 0.2s;"></div>
          <div class="absolute w-2 h-2 bg-purple-400 rounded-full top-[200px] left-[120px] animate-pulse opacity-50 shadow-md" style="animation-delay: 1.2s;"></div>
          <div class="absolute w-1.5 h-1.5 bg-yellow-400 rounded-full top-[180px] left-[300px] animate-pulse opacity-60 shadow-sm" style="animation-delay: 0.8s;"></div>

          <!-- Air quality indicator -->
          <div class="absolute w-[160px] h-[120px] top-[40px] left-[116px] text-center z-20">
            <div class="text-gray-600 text-[12px] font-medium mb-2">车内综合空气质量</div>
            <div class="text-gray-800 text-2xl font-bold mb-3">空气优</div>
            <div class="text-emerald-600 text-8xl font-black leading-none drop-shadow-2xl" style="text-shadow: 2px 2px 4px rgba(0,0,0,0.1);">25</div>
          </div>

          <!-- Simplified beautiful car using CSS -->
          <div class="absolute w-[300px] h-[160px] top-[140px] left-[46px] flex items-center justify-center z-10">
            <div class="car-container animate-float">
              <!-- Car shadow -->
              <div class="absolute w-[260px] h-[15px] bg-black/15 rounded-full bottom-[-5px] left-[20px] blur-sm"></div>

              <!-- Main car body -->
              <div class="relative w-[260px] h-[100px]">
                <!-- Car body base -->
                <div class="absolute w-full h-[65px] bg-gradient-to-r from-blue-300 via-blue-400 to-blue-500 rounded-[32px] top-[15px] shadow-xl"></div>

                <!-- Car roof -->
                <div class="absolute w-[180px] h-[40px] bg-gradient-to-b from-blue-200 to-blue-300 rounded-[20px] top-[5px] left-[40px] shadow-lg"></div>

                <!-- Car windows -->
                <div class="absolute w-[160px] h-[28px] bg-gradient-to-b from-blue-50 to-blue-100 rounded-[14px] top-[11px] left-[50px] border border-blue-200/50"></div>

                <!-- Wheels -->
                <div class="absolute w-[32px] h-[32px] bg-gray-800 rounded-full bottom-[2px] left-[35px] shadow-lg">
                  <div class="absolute inset-1.5 bg-gray-600 rounded-full">
                    <div class="absolute inset-1 bg-gray-400 rounded-full"></div>
                  </div>
                </div>
                <div class="absolute w-[32px] h-[32px] bg-gray-800 rounded-full bottom-[2px] right-[35px] shadow-lg">
                  <div class="absolute inset-1.5 bg-gray-600 rounded-full">
                    <div class="absolute inset-1 bg-gray-400 rounded-full"></div>
                  </div>
                </div>

                <!-- Car lights -->
                <div class="absolute w-[6px] h-[6px] bg-white rounded-full top-[40px] left-[6px] shadow-md"></div>
                <div class="absolute w-[6px] h-[6px] bg-red-400 rounded-full top-[40px] right-[6px] shadow-md"></div>
              </div>
            </div>
          </div>
        </div>

        <!-- Health protection days -->
        <div class="absolute top-[300px] left-0 w-full text-center">
          <span class="text-gray-600 text-[15px] font-medium">已为您健康守护 </span>
          <span class="text-gray-800 text-[19px] font-bold">231</span>
          <span class="text-gray-600 text-[15px] font-medium"> 天</span>
        </div>

        <!-- Cleaning reminder setting -->
        <div class="absolute top-[322px] left-0 w-full text-center">
          <span class="text-gray-600 text-[15px] font-medium">设置 </span>
          <span class="text-orange-500 text-[15px] font-semibold cursor-pointer hover:underline hover:text-orange-600 transition-colors">清洗提醒</span>
        </div>

        <!-- Air quality metrics -->
        <div class="absolute w-[320px] h-[60px] top-[380px] left-[36px] flex justify-around items-center">
          <div v-for="(metric, index) in airQualityMetrics" :key="index" class="text-center">
            <div class="text-gray-700 text-[32px] font-bold leading-none mb-2">
              {{ metric.value }}
            </div>
            <div class="text-gray-500 text-[11px] font-medium">
              {{ metric.label }}
            </div>
          </div>
        </div>

        <!-- Weather and air quality card -->
        <div class="absolute h-[200px] top-[460px] left-[24px] w-[345px] bg-gradient-to-br from-green-50 via-emerald-50 to-green-100 rounded-3xl shadow-xl border border-green-200/50 overflow-hidden">
          <!-- Grass texture background -->
          <div class="absolute inset-0 bg-gradient-to-t from-green-200/30 via-green-100/20 to-transparent"></div>
          
          <!-- Weather content -->
          <div class="relative z-10 h-full p-6">
            <!-- Top section -->
            <div class="flex justify-between items-start mb-4">
              <div class="flex-1">
                <div class="text-green-800 text-4xl font-bold leading-none mb-1">深圳市</div>
                <div class="text-green-600 text-xs">2025-06-09 15:07:03发布</div>
              </div>
              <div class="text-center mx-6">
                <div class="text-green-800 text-6xl font-bold leading-none">33°C</div>
              </div>
              <div class="text-right text-sm leading-tight flex-1">
                <div class="text-green-600 text-xs mb-1">空气质量: 中</div>
                <div class="text-green-600 text-xs mb-1">湿度: 52%</div>
                <div class="text-green-600 text-xs">能见度: 30km</div>
              </div>
            </div>
            
            <!-- Separator -->
            <div class="w-full h-px bg-green-300/40 my-4"></div>
            
            <!-- Bottom section -->
            <div class="flex justify-around items-center">
              <div class="text-center">
                <div class="text-orange-500 text-4xl font-bold leading-none mb-1">105</div>
                <div class="text-green-600 text-xs">车外PM2.5</div>
              </div>
              <div class="text-center">
                <div class="text-emerald-500 text-4xl font-bold leading-none mb-1">14</div>
                <div class="text-green-600 text-xs">车内PM2.5</div>
              </div>
              <div class="text-center">
                <div class="text-green-700 text-4xl font-bold leading-none mb-1">628</div>
                <div class="text-green-600 text-xs">车外负氧离子</div>
              </div>
            </div>
          </div>
          
          <!-- Decorative elements -->
          <div class="absolute top-4 right-6 w-3 h-3 bg-yellow-300 rounded-full opacity-70 animate-pulse"></div>
          <div class="absolute top-8 right-12 w-2 h-2 bg-blue-300 rounded-full opacity-50 animate-pulse" style="animation-delay: 0.5s;"></div>
        </div>

        <!-- Interactive Control panel -->
        <div class="absolute h-[80px] top-[690px] left-[24px] w-[345px] bg-white/95 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/30">
          <div class="h-full flex items-center justify-around px-8">
            <!-- Device Status -->
            <div class="flex flex-col items-center justify-center">
              <div 
                :class="`text-sm px-6 py-3 rounded-2xl font-semibold transition-all duration-300 shadow-lg ${
                  isDeviceRunning
                    ? 'bg-gradient-to-r from-emerald-500 to-emerald-600 text-white hover:from-emerald-600 hover:to-emerald-700 shadow-emerald-200'
                    : 'bg-gradient-to-r from-slate-400 to-slate-500 text-white hover:from-slate-500 hover:to-slate-600 shadow-slate-200'
                }`"
              >
                设备{{ isDeviceRunning ? '运行' : '停止' }}
              </div>
            </div>

            <!-- Power Button -->
            <div class="flex flex-col items-center justify-center">
              <button
                @click="handlePowerToggle"
                :class="`w-16 h-16 rounded-full transition-all duration-300 shadow-xl hover:shadow-2xl active:scale-95 flex items-center justify-center border-4 ${
                  isPowerOn
                    ? 'bg-gradient-to-br from-orange-400 via-orange-500 to-orange-600 text-white hover:from-orange-500 hover:to-orange-700 border-orange-300 shadow-orange-200'
                    : 'bg-gradient-to-br from-gray-300 via-gray-400 to-gray-500 text-gray-600 hover:from-gray-400 hover:to-gray-600 border-gray-200 shadow-gray-200'
                }`"
              >
                <svg width="26" height="26" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="3">
                  <path d="M18.36 6.64a9 9 0 1 1-12.73 0"/>
                  <line x1="12" y1="2" x2="12" y2="12"/>
                </svg>
              </button>
            </div>

            <!-- Level Control -->
            <div class="flex flex-col items-center justify-center">
              <button
                @click="handleLevelChange"
                :disabled="!isPowerOn"
                :class="`px-6 py-3 text-sm font-bold rounded-2xl transition-all duration-200 active:scale-90 min-w-[60px] shadow-lg ${
                  isPowerOn
                    ? 'bg-gradient-to-br from-blue-500 via-blue-600 to-blue-700 text-white hover:from-blue-600 hover:to-blue-800 border-2 border-blue-400 shadow-blue-200'
                    : 'bg-gray-100 text-gray-300 cursor-not-allowed border-2 border-gray-200 shadow-gray-100'
                }`"
              >
                {{ getLevelText(currentLevel) }}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

// Device control states
const isDeviceRunning = ref(true)
const isPowerOn = ref(true)
const currentLevel = ref(1)

// Air quality metrics data
const airQualityMetrics = [
  {
    value: "014",
    label: "PM2.5(µg/m³）",
  },
  {
    value: "36",
    label: "甲醛(µg/m³）",
  },
  {
    value: "25500",
    label: "负氧离子(个/cm³）",
  },
]

// Handle power button click
const handlePowerToggle = () => {
  isPowerOn.value = !isPowerOn.value
  if (!isPowerOn.value) {
    isDeviceRunning.value = false
  } else {
    isDeviceRunning.value = true
  }
}

// Handle level change - cycle through levels
const handleLevelChange = () => {
  if (isPowerOn.value) {
    currentLevel.value = currentLevel.value >= 3 ? 1 : currentLevel.value + 1
  }
}

// Get level display text
const getLevelText = (level: number) => {
  switch (level) {
    case 1:
      return "低"
    case 2:
      return "中"
    case 3:
      return "高"
    default:
      return "低"
  }
}
</script>

<style scoped>
/* Floating animation for car */
@keyframes float {
  0%, 100% { 
    transform: translateY(0px);
  }
  50% { 
    transform: translateY(-12px);
  }
}

.animate-float {
  animation: float 4s ease-in-out infinite;
}

.car-container {
  filter: drop-shadow(0 10px 20px rgba(0, 0, 0, 0.1));
}
</style>
