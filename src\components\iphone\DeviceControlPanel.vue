<template>
  <div class="control-panel">
    <div class="panel-content">
      <div class="controls-layout">
        <!-- 设备状态 -->
        <div class="device-status">
          <div :class="statusClasses">
            设备{{ deviceState.isRunning ? "运行" : "停止" }}
          </div>
        </div>

        <!-- 电源按钮 -->
        <div class="power-section">
          <button
            @click="$emit('powerToggle')"
            :class="powerButtonClasses"
          >
            ⚡
          </button>
        </div>

        <!-- 档位控制 -->
        <div class="level-section">
          <button
            @click="$emit('levelChange')"
            :disabled="!deviceState.isPowerOn"
            :class="levelButtonClasses"
          >
            {{ levelText }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { DeviceState } from '../../composables/useDeviceControl'

interface Props {
  deviceState: DeviceState
  levelText: string
}

const props = defineProps<Props>()

defineEmits<{
  powerToggle: []
  levelChange: []
}>()

// 计算样式类
const statusClasses = computed(() => [
  'status-badge',
  props.deviceState.isRunning ? 'status-running' : 'status-stopped'
])

const powerButtonClasses = computed(() => [
  'power-button',
  props.deviceState.isPowerOn ? 'power-on' : 'power-off'
])

const levelButtonClasses = computed(() => [
  'level-button',
  props.deviceState.isPowerOn ? 'level-enabled' : 'level-disabled'
])
</script>

<style scoped>
.control-panel {
  @apply absolute h-[74px] top-[637px] left-[22px] w-[345px] border-none shadow-lg bg-white/95 backdrop-blur-md rounded-2xl;
}

.panel-content {
  @apply p-0 h-full;
}

.controls-layout {
  @apply flex items-center h-full;
}

.device-status,
.power-section,
.level-section {
  @apply flex-1 flex flex-col items-center justify-center h-full;
}

.status-badge {
  @apply text-xs px-4 py-2 rounded-full font-medium transition-all duration-300;
}

.status-running {
  @apply bg-emerald-500 text-white shadow-md hover:bg-emerald-600;
}

.status-stopped {
  @apply bg-slate-400 text-white shadow-md hover:bg-slate-500;
}

.power-button {
  @apply w-12 h-12 rounded-full transition-all duration-300 shadow-lg hover:shadow-xl active:scale-95;
}

.power-on {
  @apply bg-gradient-to-br from-orange-400 to-orange-600 text-white hover:from-orange-500 hover:to-orange-700 border-2 border-orange-300;
}

.power-off {
  @apply bg-gradient-to-br from-gray-300 to-gray-500 text-gray-600 hover:from-gray-400 hover:to-gray-600 border-2 border-gray-200;
}

.level-button {
  @apply px-4 py-2 text-sm font-bold rounded-full transition-all duration-200 active:scale-90 min-w-[48px];
}

.level-enabled {
  @apply bg-gradient-to-br from-blue-500 to-blue-600 text-white shadow-md hover:from-blue-600 hover:to-blue-700 border border-blue-400;
}

.level-disabled {
  @apply bg-gray-50 text-gray-300 cursor-not-allowed border border-gray-200;
}
</style>
