<template>
  <div class="iphone-app">
    <!-- 使用组合式函数的数据 -->
    <div class="header">
      <h1>Vue空气质量监控</h1>
      <p>设备状态: {{ statusText }}</p>
    </div>

    <!-- 天气数据区域 -->
    <WeatherSection 
      :weather-data="weatherData"
      :loading="isLoading"
      @refresh="handleRefresh"
    />

    <!-- 设备控制区域 -->
    <DeviceControls
      :device-state="deviceState"
      :level-text="levelText"
      @power-toggle="handlePowerToggle"
      @level-change="handleLevelChange"
    />

    <!-- 空气质量指标 -->
    <AirQualityMetrics :metrics="airQualityMetrics" />
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted } from 'vue'
import { useWeatherData } from '../../composables/useWeatherData'
import { useDeviceControl } from '../../composables/useDeviceControl'
import WeatherSection from '../../components/WeatherSection.vue'
import DeviceControls from '../../components/DeviceControls.vue'
import AirQualityMetrics from '../../components/AirQualityMetrics.vue'

// 使用组合式函数
const { weatherData, isLoading, fetchWeatherData, startPolling } = useWeatherData()
const { deviceState, levelText, statusText, handlePowerToggle, handleLevelChange } = useDeviceControl()

// 计算属性
const airQualityMetrics = computed(() => [
  { id: 'pm25', value: '014', label: 'PM2.5(µg/m³)', color: 'text-green-600' },
  { id: 'formaldehyde', value: '36', label: '甲醛(µg/m³)', color: 'text-orange-500' },
  { id: 'negative_ions', value: '25500', label: '负氧离子(个/cm³)', color: 'text-blue-600' },
])

// 事件处理
const handleRefresh = () => {
  fetchWeatherData()
}

// 生命周期
onMounted(() => {
  fetchWeatherData()
  startPolling()
})
</script>

<style scoped>
.iphone-app {
  @apply min-h-screen bg-gradient-to-br from-green-50 to-blue-50 p-4;
}

.header {
  @apply text-center mb-6;
}

.header h1 {
  @apply text-2xl font-bold text-gray-800 mb-2;
}

.header p {
  @apply text-sm text-gray-600;
}
</style>
