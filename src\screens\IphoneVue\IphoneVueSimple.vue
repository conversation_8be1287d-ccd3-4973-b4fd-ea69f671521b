<template>
  <div class="bg-neutral-100 flex flex-row justify-center w-full min-h-screen">
    <div class="bg-neutral-100 w-[393px] h-[852px] relative overflow-hidden">
      <!-- Status bar -->
      <div class="absolute w-[375px] h-11 top-0 left-[11px] bg-black/10 rounded-lg flex items-center justify-between px-4">
        <span class="text-black text-sm font-medium">9:41</span>
        <div class="flex items-center gap-1">
          <div class="w-4 h-2 bg-green-500 rounded-sm"></div>
          <div class="w-6 h-3 border border-black rounded-sm">
            <div class="w-4 h-1 bg-black rounded-sm m-0.5"></div>
          </div>
        </div>
      </div>

      <div class="absolute w-[393px] h-[783px] top-[69px] left-0">
        <!-- Background with mask group effect -->
        <div class="absolute w-[349px] h-[250px] top-0 left-[22px] overflow-hidden">
          <!-- Mask group overlay image -->
          <img
            class="absolute w-full h-full object-cover"
            alt="Mask group"
            src="https://c.animaapp.com/mdqqarhondzElL/img/mask-group.png"
            @error="handleImageError"
          />

          <!-- Fallback background if image fails to load -->
          <div class="absolute inset-0 bg-gradient-radial from-green-200/60 via-blue-200/40 to-orange-200/30" v-if="imageError"></div>
          
          <!-- Floating particles -->
          <div class="absolute w-2 h-2 bg-green-400 rounded-full top-[60px] left-[80px] animate-pulse opacity-70"></div>
          <div class="absolute w-1 h-1 bg-blue-400 rounded-full top-[100px] left-[140px] animate-pulse opacity-60" style="animation-delay: 0.3s;"></div>
          <div class="absolute w-1.5 h-1.5 bg-green-500 rounded-full top-[80px] left-[240px] animate-pulse opacity-50" style="animation-delay: 0.7s;"></div>
          <div class="absolute w-1 h-1 bg-orange-400 rounded-full top-[120px] left-[60px] animate-pulse opacity-60" style="animation-delay: 0.5s;"></div>
          <div class="absolute w-2 h-2 bg-blue-500 rounded-full top-[140px] left-[210px] animate-pulse opacity-50" style="animation-delay: 1s;"></div>
          <div class="absolute w-1 h-1 bg-green-400 rounded-full top-[70px] left-[280px] animate-pulse opacity-70" style="animation-delay: 0.2s;"></div>

          <!-- Air quality logo -->
          <img
            class="absolute w-[177px] h-[90px] top-[58px] left-[86px] z-10"
            alt="Air quality logo"
            src="https://c.animaapp.com/mdqqarhondzElL/img/group-13.png"
            @error="handleImageError"
          />

          <!-- Fallback air quality indicator if image fails -->
          <div class="absolute w-[177px] h-[90px] top-[58px] left-[86px] text-center z-10" v-if="imageError">
            <div class="text-[#454545] text-[10px] font-normal mb-1">车内综合空气质量</div>
            <div class="text-[#454545] text-xl font-normal mb-1">空气优</div>
            <div class="text-[#1b705f] text-6xl font-bold leading-none">25</div>
          </div>

          <!-- Car visualization -->
          <img
            class="absolute w-[315px] h-[210px] top-[69px] left-[17px] object-cover z-5"
            alt="Car visualization"
            src="https://c.animaapp.com/mdqqarhondzElL/img/---2.png"
            @error="handleImageError"
          />

          <!-- Fallback car using CSS if image fails -->
          <div class="absolute w-[315px] h-[210px] top-[69px] left-[17px] flex items-center justify-center z-5" v-if="imageError">
            <div class="car-container">
              <!-- Car body -->
              <div class="car-body">
                <!-- Car roof -->
                <div class="car-roof"></div>
                <!-- Car windows -->
                <div class="car-windows"></div>
                <!-- Car doors -->
                <div class="car-door-left"></div>
                <div class="car-door-right"></div>
                <!-- Wheels -->
                <div class="car-wheel-left">
                  <div class="wheel-inner">
                    <div class="wheel-center"></div>
                  </div>
                </div>
                <div class="car-wheel-right">
                  <div class="wheel-inner">
                    <div class="wheel-center"></div>
                  </div>
                </div>
                <!-- Car lights -->
                <div class="car-light-left"></div>
                <div class="car-light-right"></div>
              </div>
              <!-- Car shadow -->
              <div class="car-shadow"></div>
            </div>
          </div>
        </div>

        <!-- Health protection days -->
        <div class="absolute top-64 left-[109px] text-center">
          <span class="text-[#5b5b5b] text-[15px]">已为您健康守护 </span>
          <span class="text-[#5b5b5b] text-[19px] font-medium">231</span>
          <span class="text-[#5b5b5b] text-[15px]"> 天</span>
        </div>

        <!-- Cleaning reminder setting -->
        <div class="absolute top-[282px] left-[149px] text-center">
          <span class="text-[#5b5b5b] text-[15px]">设置 </span>
          <span class="text-[#ff8800] text-[15px] font-medium cursor-pointer hover:underline">清洗提醒</span>
        </div>

        <!-- Air quality metrics -->
        <div class="absolute w-[298px] h-[45px] top-80 left-[42px] flex justify-between">
          <div v-for="(metric, index) in airQualityMetrics" :key="index" :class="`h-[45px] ${metric.width}`">
            <div class="relative h-full text-center">
              <div class="text-[#494949] text-[28px] font-normal leading-none">
                {{ metric.value }}
              </div>
              <div class="text-[#909090] text-[10px] font-normal mt-1">
                {{ metric.label }}
              </div>
            </div>
          </div>
        </div>

        <!-- Weather and air quality card -->
        <div class="absolute h-[183px] top-[409px] left-[23px] w-[345px] bg-white/60 backdrop-blur-sm rounded-2xl shadow-none border-none overflow-hidden">
          <!-- Weather background image -->
          <img
            class="absolute inset-0 w-full h-full object-cover"
            alt="Weather and air quality information"
            src="https://c.animaapp.com/mdqqarhondzElL/img/frame-1.svg"
            @error="handleImageError"
          />

          <!-- Fallback background if image fails -->
          <div class="absolute inset-0 bg-gradient-to-b from-green-50 via-green-100 to-green-200 opacity-40" v-if="imageError"></div>

          <!-- Weather content overlay (only show if image fails) -->
          <div class="relative z-10 h-full p-4" v-if="imageError">
            <!-- Top section with location and temperature -->
            <div class="flex justify-between items-start mb-3">
              <div class="flex-1">
                <div class="text-[#2d5a3d] text-3xl font-bold leading-none">深圳市</div>
                <div class="text-[#666] text-xs mt-1">2025-06-09 15:07:03发布</div>
              </div>
              <div class="text-center mx-4">
                <div class="text-[#2d5a3d] text-5xl font-bold leading-none">33°C</div>
              </div>
              <div class="text-right text-sm leading-tight flex-1">
                <div class="text-[#666] text-xs">空气质量: 中</div>
                <div class="text-[#666] text-xs">湿度: 52%</div>
                <div class="text-[#666] text-xs">能见度: 30km</div>
              </div>
            </div>

            <!-- Separator line -->
            <div class="w-full h-px bg-gray-300/50 my-3"></div>

            <!-- Bottom section with air quality data -->
            <div class="flex justify-between items-center">
              <div class="text-center flex-1">
                <div class="text-[#ff8800] text-3xl font-bold leading-none">105</div>
                <div class="text-[#666] text-xs mt-1">车外PM2.5</div>
              </div>
              <div class="text-center flex-1">
                <div class="text-[#22c55e] text-3xl font-bold leading-none">14</div>
                <div class="text-[#666] text-xs mt-1">车内PM2.5</div>
              </div>
              <div class="text-center flex-1">
                <div class="text-[#374151] text-3xl font-bold leading-none">628</div>
                <div class="text-[#666] text-xs mt-1">车外负氧离子</div>
              </div>
            </div>

            <!-- Decorative elements -->
            <div class="absolute bottom-0 left-0 w-full h-8 bg-gradient-to-t from-green-200/30 to-transparent"></div>
            <div class="absolute top-2 right-4 w-2 h-2 bg-yellow-300 rounded-full opacity-60"></div>
            <div class="absolute top-6 right-8 w-1 h-1 bg-blue-300 rounded-full opacity-40"></div>
          </div>
        </div>

        <!-- Interactive Control panel -->
        <div class="absolute h-[74px] top-[637px] left-[22px] w-[345px] bg-white/95 backdrop-blur-md rounded-2xl shadow-xl border border-white/20">
          <div class="h-full flex items-center justify-between px-6">
            <!-- Device Status -->
            <div class="flex flex-col items-center justify-center">
              <div 
                :class="`text-xs px-6 py-2.5 rounded-full font-medium transition-all duration-300 shadow-md ${
                  isDeviceRunning
                    ? 'bg-gradient-to-r from-emerald-500 to-emerald-600 text-white hover:from-emerald-600 hover:to-emerald-700'
                    : 'bg-gradient-to-r from-slate-400 to-slate-500 text-white hover:from-slate-500 hover:to-slate-600'
                }`"
              >
                设备{{ isDeviceRunning ? '运行' : '停止' }}
              </div>
            </div>

            <!-- Power Button -->
            <div class="flex flex-col items-center justify-center">
              <button
                @click="handlePowerToggle"
                :class="`w-14 h-14 rounded-full transition-all duration-300 shadow-lg hover:shadow-xl active:scale-95 flex items-center justify-center border-2 ${
                  isPowerOn
                    ? 'bg-gradient-to-br from-orange-400 to-orange-600 text-white hover:from-orange-500 hover:to-orange-700 border-orange-300 shadow-orange-200'
                    : 'bg-gradient-to-br from-gray-300 to-gray-500 text-gray-600 hover:from-gray-400 hover:to-gray-600 border-gray-200'
                }`"
              >
                <svg width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5">
                  <path d="M18.36 6.64a9 9 0 1 1-12.73 0"/>
                  <line x1="12" y1="2" x2="12" y2="12"/>
                </svg>
              </button>
            </div>

            <!-- Level Control -->
            <div class="flex flex-col items-center justify-center">
              <button
                @click="handleLevelChange"
                :disabled="!isPowerOn"
                :class="`px-6 py-2.5 text-sm font-bold rounded-full transition-all duration-200 active:scale-90 min-w-[56px] shadow-md ${
                  isPowerOn
                    ? 'bg-gradient-to-br from-blue-500 to-blue-600 text-white hover:from-blue-600 hover:to-blue-700 border border-blue-400 shadow-blue-200'
                    : 'bg-gray-100 text-gray-300 cursor-not-allowed border border-gray-200'
                }`"
              >
                {{ getLevelText(currentLevel) }}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

// Device control states
const isDeviceRunning = ref(true)
const isPowerOn = ref(true)
const currentLevel = ref(1)

// Image error handling
const imageError = ref(false)

// Air quality metrics data
const airQualityMetrics = [
  {
    value: "014",
    label: "PM2.5(µg/m³）",
    width: "w-[73px]",
  },
  {
    value: "36", 
    label: "甲醛(µg/m³）",
    width: "w-[65px]",
  },
  {
    value: "25500",
    label: "负氧离子(个/cm³）", 
    width: "w-[88px]",
  },
]

// Handle power button click
const handlePowerToggle = () => {
  isPowerOn.value = !isPowerOn.value
  if (!isPowerOn.value) {
    isDeviceRunning.value = false
  } else {
    isDeviceRunning.value = true
  }
}

// Handle level change - cycle through levels
const handleLevelChange = () => {
  if (isPowerOn.value) {
    currentLevel.value = currentLevel.value >= 3 ? 1 : currentLevel.value + 1
  }
}

// Get level display text
const getLevelText = (level: number) => {
  switch (level) {
    case 1:
      return "低"
    case 2:
      return "中"
    case 3:
      return "高"
    default:
      return "低"
  }
}

// Handle image loading errors
const handleImageError = () => {
  imageError.value = true
}
</script>

<style scoped>
.bg-gradient-radial {
  background: radial-gradient(circle at center, var(--tw-gradient-stops));
}

/* Car styling */
.car-container {
  position: relative;
  width: 240px;
  height: 120px;
  transform: scale(0.8);
}

.car-body {
  position: absolute;
  width: 240px;
  height: 80px;
  background: linear-gradient(135deg, #bfdbfe 0%, #93c5fd 50%, #60a5fa 100%);
  border-radius: 24px;
  top: 20px;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.car-roof {
  position: absolute;
  width: 160px;
  height: 32px;
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  border-radius: 16px;
  top: -8px;
  left: 40px;
}

.car-windows {
  position: absolute;
  width: 140px;
  height: 24px;
  background: rgba(135, 206, 235, 0.6);
  border: 1px solid rgba(70, 130, 180, 0.3);
  border-radius: 12px;
  top: -4px;
  left: 50px;
}

.car-door-left {
  position: absolute;
  width: 48px;
  height: 48px;
  background: rgba(147, 197, 253, 0.4);
  border-radius: 8px;
  border-left: 2px solid rgba(96, 165, 250, 0.4);
  top: 16px;
  left: 16px;
}

.car-door-right {
  position: absolute;
  width: 48px;
  height: 48px;
  background: rgba(147, 197, 253, 0.4);
  border-radius: 8px;
  border-right: 2px solid rgba(96, 165, 250, 0.4);
  top: 16px;
  right: 16px;
}

.car-wheel-left, .car-wheel-right {
  position: absolute;
  width: 32px;
  height: 32px;
  background: #2d3748;
  border-radius: 50%;
  bottom: -16px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.car-wheel-left {
  left: 32px;
}

.car-wheel-right {
  right: 32px;
}

.wheel-inner {
  position: absolute;
  width: 20px;
  height: 20px;
  background: #4a5568;
  border-radius: 50%;
  top: 6px;
  left: 6px;
}

.wheel-center {
  position: absolute;
  width: 8px;
  height: 8px;
  background: #718096;
  border-radius: 50%;
  top: 6px;
  left: 6px;
}

.car-light-left, .car-light-right {
  position: absolute;
  width: 8px;
  height: 8px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  top: 24px;
}

.car-light-left {
  left: 4px;
}

.car-light-right {
  right: 4px;
}

.car-shadow {
  position: absolute;
  width: 200px;
  height: 16px;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  bottom: 0;
  left: 20px;
  filter: blur(4px);
}

/* Animation */
@keyframes float {
  0%, 100% { transform: translateY(0px) scale(0.8); }
  50% { transform: translateY(-8px) scale(0.8); }
}

.car-container {
  animation: float 3s ease-in-out infinite;
}
</style>
