<template>
  <div class="background-container">
    <!-- 背景图片 -->
    <img
      class="background-image"
      alt="Background"
      src="https://c.animaapp.com/mdqqarhondzElL/img/----.png"
    />

    <!-- 圆形渐变元素 -->
    <div class="gradient-elements">
      <img
        class="outer-ellipse"
        alt="Outer ellipse"
        src="https://c.animaapp.com/mdqqarhondzElL/img/ellipse-11.svg"
      />

      <img
        class="middle-ellipse"
        alt="Middle ellipse"
        src="https://c.animaapp.com/mdqqarhondzElL/img/ellipse-12.svg"
      />

      <img
        class="inner-ellipse"
        alt="Inner ellipse"
        src="https://c.animaapp.com/mdqqarhondzElL/img/ellipse-13.svg"
      />

      <!-- 汽车可视化 -->
      <img
        class="car-visualization"
        alt="Car visualization"
        src="https://c.animaapp.com/mdqqarhondzElL/img/---2.png"
      />

      <!-- 遮罩组 -->
      <img
        class="mask-group"
        alt="Mask group"
        src="https://c.animaapp.com/mdqqarhondzElL/img/mask-group.png"
      />
    </div>
  </div>
</template>

<style scoped>
.background-container {
  @apply absolute w-[393px] h-[783px] top-[69px] left-0;
}

.background-image {
  @apply absolute w-[393px] h-[619px] top-[164px] left-0 object-cover;
}

.gradient-elements {
  @apply absolute w-full top-0 left-0;
}

.outer-ellipse {
  @apply absolute w-[311px] h-[311px] top-4 left-10 rounded-full aspect-square object-cover;
}

.middle-ellipse {
  @apply absolute w-[231px] h-[231px] top-[56px] left-[50px] rounded-full aspect-square object-cover;
}

.inner-ellipse {
  @apply absolute w-[175px] h-[175px] top-[84px] left-[78px] rounded-full aspect-square object-cover;
}

.car-visualization {
  @apply absolute w-[315px] h-[210px] top-[69px] left-[39px] object-cover;
}

.mask-group {
  @apply absolute w-[349px] h-[250px] top-0 left-[22px];
}
</style>
