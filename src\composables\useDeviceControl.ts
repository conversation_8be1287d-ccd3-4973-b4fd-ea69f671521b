import { ref, computed, watch } from 'vue'

export type DeviceLevel = 1 | 2 | 3

export interface DeviceState {
  isRunning: boolean
  isPowerOn: boolean
  currentLevel: DeviceLevel
}

export function useDeviceControl() {
  const isRunning = ref(true)
  const isPowerOn = ref(true)
  const currentLevel = ref<DeviceLevel>(1)

  // 计算属性：设备状态
  const deviceState = computed<DeviceState>(() => ({
    isRunning: isRunning.value,
    isPowerOn: isPowerOn.value,
    currentLevel: currentLevel.value
  }))

  // 计算属性：档位文本
  const levelText = computed(() => {
    const levelMap = {
      1: '低',
      2: '中', 
      3: '高'
    }
    return levelMap[currentLevel.value]
  })

  // 计算属性：设备状态文本
  const statusText = computed(() => 
    isRunning.value ? '运行中' : '已停止'
  )

  // 电源开关处理
  const handlePowerToggle = () => {
    isPowerOn.value = !isPowerOn.value
  }

  // 档位切换处理
  const handleLevelChange = () => {
    if (!isPowerOn.value) return
    
    currentLevel.value = (currentLevel.value >= 3 ? 1 : currentLevel.value + 1) as DeviceLevel
  }

  // 监听电源状态变化，自动更新运行状态
  watch(isPowerOn, (newValue) => {
    isRunning.value = newValue
  })

  // 设备控制方法
  const turnOn = () => {
    isPowerOn.value = true
  }

  const turnOff = () => {
    isPowerOn.value = false
  }

  const setLevel = (level: DeviceLevel) => {
    if (isPowerOn.value) {
      currentLevel.value = level
    }
  }

  return {
    // 状态
    deviceState,
    levelText,
    statusText,
    
    // 方法
    handlePowerToggle,
    handleLevelChange,
    turnOn,
    turnOff,
    setLevel
  }
}
