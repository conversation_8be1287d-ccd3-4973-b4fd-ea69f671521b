<template>
  <div class="weather-section">
    <div class="weather-card">
      <div class="card-header">
        <h2>天气信息</h2>
        <button @click="$emit('refresh')" class="refresh-btn">
          🔄 刷新
        </button>
      </div>

      <div v-if="loading" class="loading">
        <div class="spinner"></div>
        <p>加载中...</p>
      </div>

      <div v-else-if="weatherData" class="weather-content">
        <div class="main-info">
          <div class="city">{{ weatherData.city }}</div>
          <div class="temperature">{{ weatherData.temperature }}°C</div>
          <div class="air-quality">空气质量: {{ weatherData.air_quality }}</div>
        </div>

        <div class="details-grid">
          <div class="detail-item">
            <span class="label">湿度</span>
            <span class="value">{{ weatherData.humidity }}%</span>
          </div>
          <div class="detail-item">
            <span class="label">能见度</span>
            <span class="value">{{ weatherData.visibility }}km</span>
          </div>
        </div>

        <div class="pm-grid">
          <div class="pm-item outdoor">
            <span class="pm-label">车外PM2.5</span>
            <span class="pm-value">{{ weatherData.pm25_outdoor }}</span>
          </div>
          <div class="pm-item indoor">
            <span class="pm-label">车内PM2.5</span>
            <span class="pm-value">{{ weatherData.pm25_indoor }}</span>
          </div>
          <div class="pm-item ions">
            <span class="pm-label">负氧离子</span>
            <span class="pm-value">{{ weatherData.negative_ions }}</span>
          </div>
        </div>

        <div class="publish-time">
          {{ weatherData.publish_time }} 发布
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { WeatherData } from '../composables/useWeatherData'

interface Props {
  weatherData: WeatherData | null
  loading: boolean
}

defineProps<Props>()
defineEmits<{
  refresh: []
}>()
</script>

<style scoped>
.weather-section {
  @apply mb-6;
}

.weather-card {
  @apply bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg;
}

.card-header {
  @apply flex justify-between items-center mb-4;
}

.card-header h2 {
  @apply text-lg font-semibold text-gray-800;
}

.refresh-btn {
  @apply text-sm text-blue-600 hover:text-blue-800 transition-colors;
}

.loading {
  @apply flex flex-col items-center py-8;
}

.spinner {
  @apply w-6 h-6 border-2 border-blue-600 border-t-transparent rounded-full animate-spin mb-2;
}

.weather-content {
  @apply space-y-4;
}

.main-info {
  @apply text-center space-y-2;
}

.city {
  @apply text-xl font-bold text-gray-800;
}

.temperature {
  @apply text-4xl font-bold text-green-600;
}

.air-quality {
  @apply text-sm text-gray-600;
}

.details-grid {
  @apply grid grid-cols-2 gap-4;
}

.detail-item {
  @apply flex justify-between items-center p-3 bg-gray-50 rounded-lg;
}

.label {
  @apply text-sm text-gray-600;
}

.value {
  @apply font-semibold text-gray-800;
}

.pm-grid {
  @apply grid grid-cols-3 gap-3;
}

.pm-item {
  @apply text-center p-3 rounded-lg;
}

.pm-item.outdoor {
  @apply bg-orange-50;
}

.pm-item.indoor {
  @apply bg-green-50;
}

.pm-item.ions {
  @apply bg-blue-50;
}

.pm-label {
  @apply block text-xs text-gray-600 mb-1;
}

.pm-value {
  @apply block text-lg font-bold;
}

.outdoor .pm-value {
  @apply text-orange-600;
}

.indoor .pm-value {
  @apply text-green-600;
}

.ions .pm-value {
  @apply text-blue-600;
}

.publish-time {
  @apply text-xs text-gray-500 text-center;
}
</style>
