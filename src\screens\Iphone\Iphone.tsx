import React, { useState } from "react";
import { Card, CardContent } from "../../components/ui/card";
import { Button } from "../../components/ui/button";
import { Badge } from "../../components/ui/badge";
import { Power, Settings } from "lucide-react";

export const Iphone = (): JSX.Element => {
  // Device control states
  const [isDeviceRunning, setIsDeviceRunning] = useState(true);
  const [isPowerOn, setIsPowerOn] = useState(true);
  const [currentLevel, setCurrentLevel] = useState(1);

  // Air quality metrics data
  const airQualityMetrics = [
    {
      value: "014",
      label: "PM2.5(µg/m³）",
      width: "w-[73px]",
    },
    {
      value: "36",
      label: "甲醛(µg/m³）",
      width: "w-[65px]",
    },
    {
      value: "25500",
      label: "负氧离子(个/cm³）",
      width: "w-[88px]",
    },
  ];

  // Handle power button click
  const handlePowerToggle = () => {
    setIsPowerOn(!isPowerOn);
    if (!isPowerOn) {
      setIsDeviceRunning(true);
    } else {
      setIsDeviceRunning(false);
    }
  };

  // Handle level change - cycle through levels
  const handleLevelChange = () => {
    if (isPowerOn) {
      setCurrentLevel((prev) => (prev >= 3 ? 1 : prev + 1));
    }
  };

  // Get level display text
  const getLevelText = (level: number) => {
    switch (level) {
      case 1:
        return "低";
      case 2:
        return "中";
      case 3:
        return "高";
      default:
        return "低";
    }
  };

  return (
    <div
      className="bg-neutral-100 flex flex-row justify-center w-full"
      data-model-id="107:9"
    >
      <div className="bg-neutral-100 w-[393px] h-[852px] relative">
        {/* Status bar */}
        <img
          className="absolute w-[375px] h-11 top-0 left-[11px]"
          alt="Status bar"
          src="https://c.animaapp.com/mdqqarhondzElL/img/---------.svg"
        />

        <div className="absolute w-[393px] h-[783px] top-[69px] left-0">
          {/* Background image */}
          <img
            className="absolute w-[393px] h-[619px] top-[164px] left-0 object-cover"
            alt="Background"
            src="https://c.animaapp.com/mdqqarhondzElL/img/----.png"
          />

          {/* Circular gradient elements */}
          <div className="absolute w-full top-0 left-0">
            <img
              className="absolute w-[311px] h-[311px] top-4 left-10 rounded-full aspect-square object-cover"
              alt="Outer ellipse"
              src="https://c.animaapp.com/mdqqarhondzElL/img/ellipse-11.svg"
            />

            <img
              className="absolute w-[231px] h-[231px] top-[56px] left-[50px] rounded-full aspect-square object-cover"
              alt="Middle ellipse"
              src="https://c.animaapp.com/mdqqarhondzElL/img/ellipse-12.svg"
            />

            <img
              className="absolute w-[175px] h-[175px] top-[84px] left-[78px] rounded-full aspect-square object-cover"
              alt="Inner ellipse"
              src="https://c.animaapp.com/mdqqarhondzElL/img/ellipse-13.svg"
            />

            {/* Car visualization */}
            <img
              className="absolute w-[315px] h-[210px] top-[69px] left-[39px] object-cover"
              alt="Car visualization"
              src="https://c.animaapp.com/mdqqarhondzElL/img/---2.png"
            />

            {/* Air quality indicator */}
            <div className="absolute w-[75px] h-24 top-[49px] left-[166px]">
              <div className="absolute w-[69px] h-[87px] top-[9px] left-0">
                <div className="top-0 left-1 text-[#454545] text-xl whitespace-nowrap absolute [font-family:'HarmonyOS_Sans-Regular',Helvetica] font-normal tracking-[0] leading-[normal]">
                  空气优
                </div>

                <div className="top-[17px] left-0 text-[#1b705f] text-6xl absolute [font-family:'HarmonyOS_Sans-Regular',Helvetica] font-normal tracking-[0] leading-[normal] whitespace-nowrap">
                  25
                </div>
              </div>

              <div className="top-0 left-2.5 text-[#454545] text-[6px] whitespace-nowrap absolute [font-family:'HarmonyOS_Sans-Regular',Helvetica] font-normal tracking-[0] leading-[normal]">
                车内综合空气质量
              </div>
            </div>

            {/* Air quality logo - Commented out for dynamic version */}
            {/* <img
              className="absolute w-[177px] h-[90px] top-[58px] left-[105px]"
              alt="Air quality logo"
              src="https://c.animaapp.com/mdqqarhondzElL/img/group-13.png"
            /> */}

            {/* Mask group overlay - Commented out for dynamic version */}
            {/* <img
              className="absolute w-[349px] h-[250px] top-0 left-[22px]"
              alt="Mask group"
              src="https://c.animaapp.com/mdqqarhondzElL/img/mask-group.png"
            /> */}
          </div>

          {/* Health protection days */}
          <div className="top-64 left-[109px] text-transparent text-[15px] absolute [font-family:'HarmonyOS_Sans-Regular',Helvetica] font-normal tracking-[0] leading-[normal]">
            <span className="text-[#5b5b5b]">已为您健康守护 </span>
            <span className="text-[#5b5b5b] text-[19px]">231</span>
            <span className="text-[#5b5b5b]"> 天</span>
          </div>

          {/* Cleaning reminder setting */}
          <div className="top-[282px] left-[149px] text-transparent text-[15px] whitespace-nowrap absolute [font-family:'HarmonyOS_Sans-Regular',Helvetica] font-normal tracking-[0] leading-[normal]">
            <span className="text-[#5b5b5b]">设置 </span>
            <span className="text-[#ff8800]">清洗提醒</span>
          </div>

          {/* Air quality metrics */}
          <div className="absolute w-[298px] h-[45px] top-80 left-[42px] flex justify-between">
            {airQualityMetrics.map((metric, index) => (
              <div key={index} className={`h-[45px] ${metric.width}`}>
                <div className="relative h-full">
                  <div className="absolute top-0 left-1.5 [font-family:'HarmonyOS_Sans-Regular',Helvetica] font-normal text-[#494949] text-[28px] tracking-[0] leading-[normal] whitespace-nowrap">
                    {metric.value}
                  </div>
                  <div className="absolute top-[33px] left-0 [font-family:'HarmonyOS_Sans-Regular',Helvetica] font-normal text-[#909090] text-[10px] tracking-[0] leading-[normal] whitespace-nowrap">
                    {metric.label}
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Weather and air quality card - Exact layout matching screenshot */}
          <div className="absolute h-[183px] top-[409px] left-[23px] w-[345px] bg-white/60 backdrop-blur-sm rounded-2xl overflow-hidden">
            <div className="relative h-full">
              {/* Top section with proper positioning */}
              <div className="absolute top-4 left-4 right-4">
                <div className="flex justify-between items-start">
                  {/* Left: 深圳市 and date */}
                  <div>
                    <div className="text-green-700 text-[28px] font-bold leading-tight mb-1 [font-family:'HarmonyOS_Sans-Regular',Helvetica]">深圳市</div>
                    <div className="text-green-600 text-[9px] leading-tight [font-family:'HarmonyOS_Sans-Regular',Helvetica]">2025-06-09</div>
                    <div className="text-green-600 text-[9px] leading-tight [font-family:'HarmonyOS_Sans-Regular',Helvetica]">15:07:03发布</div>
                  </div>

                  {/* Center: Temperature - positioned more to the right */}
                  <div className="absolute top-0 left-[120px]">
                    <div className="text-green-700 text-[56px] font-bold leading-none [font-family:'HarmonyOS_Sans-Regular',Helvetica]">33°C</div>
                  </div>

                  {/* Right: Air quality info */}
                  <div className="text-right text-green-600 text-[10px] leading-tight [font-family:'HarmonyOS_Sans-Regular',Helvetica]">
                    <div className="mb-1">空气质量: 中</div>
                    <div className="mb-1">湿度: 52%</div>
                    <div>能见度: 30km</div>
                  </div>
                </div>
              </div>

              {/* Bottom section: PM2.5 data */}
              <div className="absolute bottom-6 left-4 right-4">
                <div className="flex justify-between items-end">
                  <div className="text-center">
                    <div className="text-orange-500 text-[32px] font-bold leading-none mb-1 [font-family:'HarmonyOS_Sans-Regular',Helvetica]">105</div>
                    <div className="text-green-600 text-[9px] [font-family:'HarmonyOS_Sans-Regular',Helvetica]">车外PM2.5</div>
                  </div>
                  <div className="text-center">
                    <div className="text-emerald-500 text-[32px] font-bold leading-none mb-1 [font-family:'HarmonyOS_Sans-Regular',Helvetica]">14</div>
                    <div className="text-green-600 text-[9px] [font-family:'HarmonyOS_Sans-Regular',Helvetica]">车内PM2.5</div>
                  </div>
                  <div className="text-center">
                    <div className="text-green-700 text-[32px] font-bold leading-none mb-1 [font-family:'HarmonyOS_Sans-Regular',Helvetica]">628</div>
                    <div className="text-green-600 text-[9px] [font-family:'HarmonyOS_Sans-Regular',Helvetica]">车外负氧离子</div>
                  </div>
                </div>
              </div>

              {/* Refresh icon */}
              <div className="absolute top-4 right-4 text-orange-400 text-[10px] [font-family:'HarmonyOS_Sans-Regular',Helvetica]">刷新</div>
            </div>
          </div>

          {/* Interactive Control panel */}
          <Card className="absolute h-[74px] top-[637px] left-[22px] w-[345px] border-none shadow-lg bg-white/95 backdrop-blur-md rounded-2xl">
            <CardContent className="p-0 h-full">
              <div className="flex items-center h-full">
                {/* Device Status */}
                <div className="flex-1 flex flex-col items-center justify-center h-full">
                  <Badge
                    variant="secondary"
                    className={`text-xs px-4 py-2 rounded-full font-medium transition-all duration-300 ${
                      isDeviceRunning
                        ? "bg-emerald-500 text-white shadow-md hover:bg-emerald-600"
                        : "bg-slate-400 text-white shadow-md hover:bg-slate-500"
                    }`}
                  >
                    设备{isDeviceRunning ? "运行" : "停止"}
                  </Badge>
                </div>

                {/* Power Button */}
                <div className="flex-1 flex flex-col items-center justify-center h-full">
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={handlePowerToggle}
                    className={`w-12 h-12 rounded-full transition-all duration-300 shadow-lg hover:shadow-xl active:scale-95 ${
                      isPowerOn
                        ? "bg-gradient-to-br from-orange-400 to-orange-600 text-white hover:from-orange-500 hover:to-orange-700 border-2 border-orange-300"
                        : "bg-gradient-to-br from-gray-300 to-gray-500 text-gray-600 hover:from-gray-400 hover:to-gray-600 border-2 border-gray-200"
                    }`}
                  >
                    <Power size={20} className="drop-shadow-sm" />
                  </Button>
                </div>

                {/* Level Control */}
                <div className="flex-1 flex flex-col items-center justify-center h-full">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleLevelChange}
                    disabled={!isPowerOn}
                    className={`px-4 py-2 text-sm font-bold rounded-full transition-all duration-200 active:scale-90 min-w-[48px] ${
                      isPowerOn
                        ? "bg-gradient-to-br from-blue-500 to-blue-600 text-white shadow-md hover:from-blue-600 hover:to-blue-700 border border-blue-400"
                        : "bg-gray-50 text-gray-300 cursor-not-allowed border border-gray-200"
                    }`}
                  >
                    {getLevelText(currentLevel)}
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};
