<template>
  <div class="device-controls">
    <div class="controls-card">
      <h3>设备控制</h3>
      
      <div class="controls-grid">
        <!-- 设备状态显示 -->
        <div class="status-display">
          <div class="status-label">设备状态</div>
          <div :class="statusClass">
            {{ deviceState.isRunning ? '运行中' : '已停止' }}
          </div>
        </div>

        <!-- 电源按钮 -->
        <div class="power-control">
          <div class="control-label">电源</div>
          <button 
            @click="$emit('powerToggle')"
            :class="powerButtonClass"
          >
            {{ deviceState.isPowerOn ? 'ON' : 'OFF' }}
          </button>
        </div>

        <!-- 档位控制 -->
        <div class="level-control">
          <div class="control-label">档位</div>
          <button
            @click="$emit('levelChange')"
            :disabled="!deviceState.isPowerOn"
            :class="levelButtonClass"
          >
            {{ levelText }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { DeviceState } from '../composables/useDeviceControl'

interface Props {
  deviceState: DeviceState
  levelText: string
}

const props = defineProps<Props>()

defineEmits<{
  powerToggle: []
  levelChange: []
}>()

// 计算样式类
const statusClass = computed(() => [
  'status-badge',
  props.deviceState.isRunning ? 'status-running' : 'status-stopped'
])

const powerButtonClass = computed(() => [
  'power-button',
  props.deviceState.isPowerOn ? 'power-on' : 'power-off'
])

const levelButtonClass = computed(() => [
  'level-button',
  props.deviceState.isPowerOn ? 'level-enabled' : 'level-disabled'
])
</script>

<style scoped>
.device-controls {
  @apply mb-6;
}

.controls-card {
  @apply bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg;
}

.controls-card h3 {
  @apply text-lg font-semibold text-gray-800 mb-4;
}

.controls-grid {
  @apply grid grid-cols-3 gap-4;
}

.status-display,
.power-control,
.level-control {
  @apply text-center;
}

.status-label,
.control-label {
  @apply text-sm text-gray-600 mb-2 block;
}

.status-badge {
  @apply px-3 py-2 rounded-full text-sm font-medium transition-colors;
}

.status-running {
  @apply bg-green-500 text-white;
}

.status-stopped {
  @apply bg-gray-400 text-white;
}

.power-button {
  @apply w-12 h-12 rounded-full font-bold text-lg transition-all duration-300 shadow-md hover:shadow-lg active:scale-95;
}

.power-on {
  @apply bg-gradient-to-br from-orange-400 to-orange-600 text-white hover:from-orange-500 hover:to-orange-700;
}

.power-off {
  @apply bg-gradient-to-br from-gray-300 to-gray-500 text-gray-600 hover:from-gray-400 hover:to-gray-600;
}

.level-button {
  @apply px-4 py-2 rounded-full font-bold text-sm transition-all duration-200 active:scale-90 min-w-[48px];
}

.level-enabled {
  @apply bg-gradient-to-br from-blue-500 to-blue-600 text-white shadow-md hover:from-blue-600 hover:to-blue-700;
}

.level-disabled {
  @apply bg-gray-50 text-gray-300 cursor-not-allowed;
}
</style>
