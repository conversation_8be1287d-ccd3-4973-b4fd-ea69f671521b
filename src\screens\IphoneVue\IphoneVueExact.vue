<template>
  <div class="bg-neutral-100 flex flex-row justify-center w-full min-h-screen">
    <div class="bg-neutral-100 w-[393px] h-[852px] relative overflow-hidden">
      <!-- Status bar -->
      <img
        class="absolute w-[375px] h-11 top-0 left-[11px]"
        alt="Status bar"
        src="https://c.animaapp.com/mdqqarhondzElL/img/---------.svg"
      />

      <div class="absolute w-[393px] h-[783px] top-[69px] left-0">
        <!-- Background mask group image -->
        <img
          class="absolute w-[349px] h-[250px] top-0 left-[22px]"
          alt="Mask group"
          src="https://c.animaapp.com/mdqqarhondzElL/img/mask-group.png"
        />

        <!-- Air quality logo -->
        <img
          class="absolute w-[177px] h-[90px] top-[58px] left-[108px] z-10"
          alt="Air quality logo"
          src="https://c.animaapp.com/mdqqarhondzElL/img/group-13.png"
        />

        <!-- Car visualization -->
        <img
          class="absolute w-[315px] h-[210px] top-[69px] left-[39px] z-5"
          alt="Car visualization"
          src="https://c.animaapp.com/mdqqarhondzElL/img/---2.png"
        />

        <!-- Health protection days -->
        <div class="absolute top-64 left-[109px] text-center">
          <span class="text-[#5b5b5b] text-[15px] font-normal">已为您健康守护 </span>
          <span class="text-[#5b5b5b] text-[19px] font-medium">231</span>
          <span class="text-[#5b5b5b] text-[15px] font-normal"> 天</span>
        </div>

        <!-- Cleaning reminder setting -->
        <div class="absolute top-[282px] left-[149px] text-center">
          <span class="text-[#5b5b5b] text-[15px] font-normal">设置 </span>
          <span class="text-[#ff8800] text-[15px] font-medium cursor-pointer hover:underline">清洗提醒</span>
        </div>

        <!-- Air quality metrics -->
        <div class="absolute w-[298px] h-[45px] top-80 left-[47px] flex justify-between">
          <div v-for="(metric, index) in airQualityMetrics" :key="index" :class="`h-[45px] ${metric.width}`">
            <div class="relative h-full text-center">
              <div class="text-[#494949] text-[28px] font-normal leading-none">
                {{ metric.value }}
              </div>
              <div class="text-[#909090] text-[10px] font-normal mt-1">
                {{ metric.label }}
              </div>
            </div>
          </div>
        </div>

        <!-- Weather and air quality card -->
        <img
          class="absolute h-[183px] top-[409px] left-[23px] w-[345px]"
          alt="Weather and air quality information"
          src="https://c.animaapp.com/mdqqarhondzElL/img/frame-1.svg"
        />

        <!-- Interactive Control panel -->
        <div class="absolute h-[74px] top-[637px] left-[22px] w-[345px] bg-white/90 backdrop-blur-md rounded-2xl shadow-lg">
          <div class="h-full flex items-center justify-around px-6">
            <!-- Device Status -->
            <div class="flex flex-col items-center justify-center">
              <div
                :class="`text-xs px-5 py-2 rounded-full font-medium transition-all duration-300 ${
                  isDeviceRunning
                    ? 'bg-emerald-500 text-white shadow-sm'
                    : 'bg-slate-400 text-white shadow-sm'
                }`"
              >
                设备{{ isDeviceRunning ? '运行' : '停止' }}
              </div>
            </div>

            <!-- Power Button -->
            <div class="flex flex-col items-center justify-center">
              <button
                @click="handlePowerToggle"
                :class="`w-14 h-14 rounded-full transition-all duration-300 shadow-md hover:shadow-lg active:scale-95 flex items-center justify-center ${
                  isPowerOn
                    ? 'bg-gradient-to-br from-orange-400 to-orange-600 text-white'
                    : 'bg-gradient-to-br from-gray-300 to-gray-500 text-gray-600'
                }`"
              >
                <svg width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5">
                  <path d="M18.36 6.64a9 9 0 1 1-12.73 0"/>
                  <line x1="12" y1="2" x2="12" y2="12"/>
                </svg>
              </button>
            </div>

            <!-- Level Control -->
            <div class="flex flex-col items-center justify-center">
              <button
                @click="handleLevelChange"
                :disabled="!isPowerOn"
                :class="`px-5 py-2 text-sm font-bold rounded-full transition-all duration-200 active:scale-90 min-w-[50px] ${
                  isPowerOn
                    ? 'bg-gradient-to-br from-blue-500 to-blue-600 text-white shadow-sm'
                    : 'bg-gray-100 text-gray-300 cursor-not-allowed'
                }`"
              >
                {{ getLevelText(currentLevel) }}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

// Device control states
const isDeviceRunning = ref(true)
const isPowerOn = ref(true)
const currentLevel = ref(1)

// Air quality metrics data
const airQualityMetrics = [
  {
    value: "014",
    label: "PM2.5(µg/m³）",
    width: "w-[73px]",
  },
  {
    value: "36",
    label: "甲醛(µg/m³）",
    width: "w-[65px]",
  },
  {
    value: "25500",
    label: "负氧离子(个/cm³）",
    width: "w-[88px]",
  },
]

// Handle power button click
const handlePowerToggle = () => {
  isPowerOn.value = !isPowerOn.value
  if (!isPowerOn.value) {
    isDeviceRunning.value = false
  } else {
    isDeviceRunning.value = true
  }
}

// Handle level change - cycle through levels
const handleLevelChange = () => {
  if (isPowerOn.value) {
    currentLevel.value = currentLevel.value >= 3 ? 1 : currentLevel.value + 1
  }
}

// Get level display text
const getLevelText = (level: number) => {
  switch (level) {
    case 1:
      return "低"
    case 2:
      return "中"
    case 3:
      return "高"
    default:
      return "低"
  }
}
</script>

<style scoped>
/* Custom font family for Chinese text */
body {
  font-family: 'HarmonyOS Sans', 'PingFang SC', 'Microsoft YaHei', sans-serif;
}
</style>
