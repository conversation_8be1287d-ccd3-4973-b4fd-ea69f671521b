<template>
  <div class="min-h-screen bg-gradient-to-br from-green-50 to-blue-50 p-4">
    <div class="max-w-md mx-auto bg-white/80 backdrop-blur-sm rounded-3xl shadow-xl overflow-hidden">
      
      <!-- 头部状态栏 -->
      <div class="bg-green-600 text-white p-4 text-center">
        <h1 class="text-lg font-bold">空气质量监控</h1>
        <div class="text-sm opacity-90">{{ currentTime }}</div>
      </div>

      <!-- 主要天气信息 -->
      <div class="p-6 space-y-6">
        
        <!-- 城市和温度 -->
        <div class="text-center space-y-2">
          <h2 class="text-2xl font-bold text-gray-800">{{ weatherData?.city || "深圳市" }}</h2>
          <div class="text-6xl font-bold text-green-600">{{ weatherData?.temperature || 33 }}°C</div>
          <div class="text-sm text-gray-500">{{ weatherData?.publish_time || "2025-06-09 15:07:03" }} 发布</div>
        </div>

        <!-- 空气质量信息 -->
        <div class="bg-green-50 rounded-2xl p-4">
          <h3 class="text-lg font-semibold text-green-800 mb-3">环境信息</h3>
          <div class="grid grid-cols-3 gap-4 text-center">
            <div>
              <div class="text-sm text-gray-600">空气质量</div>
              <div class="text-lg font-bold text-green-600">{{ weatherData?.air_quality || "中" }}</div>
            </div>
            <div>
              <div class="text-sm text-gray-600">湿度</div>
              <div class="text-lg font-bold text-blue-600">{{ weatherData?.humidity || 52 }}%</div>
            </div>
            <div>
              <div class="text-sm text-gray-600">能见度</div>
              <div class="text-lg font-bold text-gray-600">{{ weatherData?.visibility || 30 }}km</div>
            </div>
          </div>
        </div>

        <!-- PM2.5 和负氧离子 -->
        <div class="grid grid-cols-3 gap-4">
          <div class="bg-orange-50 rounded-xl p-4 text-center">
            <div class="text-xs text-gray-600 mb-1">车外PM2.5</div>
            <div class="text-2xl font-bold text-orange-500">{{ weatherData?.pm25_outdoor || "105" }}</div>
          </div>
          <div class="bg-emerald-50 rounded-xl p-4 text-center">
            <div class="text-xs text-gray-600 mb-1">车内PM2.5</div>
            <div class="text-2xl font-bold text-emerald-500">{{ weatherData?.pm25_indoor || "14" }}</div>
          </div>
          <div class="bg-green-50 rounded-xl p-4 text-center">
            <div class="text-xs text-gray-600 mb-1">负氧离子</div>
            <div class="text-2xl font-bold text-green-600">{{ weatherData?.negative_ions || "628" }}</div>
          </div>
        </div>

        <!-- 设备控制 -->
        <div class="bg-gray-50 rounded-2xl p-4">
          <h3 class="text-lg font-semibold text-gray-800 mb-4">设备控制</h3>
          <div class="flex justify-between items-center">
            
            <!-- 设备状态 -->
            <div class="text-center">
              <div class="text-sm text-gray-600 mb-2">设备状态</div>
              <div :class="isDeviceRunning ? 'bg-green-500 text-white' : 'bg-gray-400 text-white'" 
                   class="px-4 py-2 rounded-full text-sm font-medium">
                {{ isDeviceRunning ? "运行中" : "已停止" }}
              </div>
            </div>

            <!-- 电源按钮 -->
            <div class="text-center">
              <div class="text-sm text-gray-600 mb-2">电源</div>
              <button @click="handlePowerToggle" 
                      :class="isPowerOn ? 'bg-orange-500 hover:bg-orange-600' : 'bg-gray-400 hover:bg-gray-500'"
                      class="w-12 h-12 rounded-full text-white font-bold text-xl transition-colors">
                {{ isPowerOn ? "ON" : "OFF" }}
              </button>
            </div>

            <!-- 档位控制 -->
            <div class="text-center">
              <div class="text-sm text-gray-600 mb-2">档位</div>
              <button @click="handleLevelChange" 
                      :disabled="!isPowerOn"
                      :class="isPowerOn ? 'bg-blue-500 hover:bg-blue-600 text-white' : 'bg-gray-200 text-gray-400 cursor-not-allowed'"
                      class="px-4 py-2 rounded-full font-bold transition-colors">
                {{ getLevelText(currentLevel) }}
              </button>
            </div>
          </div>
        </div>

        <!-- 刷新按钮 -->
        <div class="text-center">
          <button @click="fetchWeatherData" 
                  :disabled="isLoading"
                  class="bg-green-500 hover:bg-green-600 disabled:bg-gray-400 text-white px-6 py-2 rounded-full font-medium transition-colors">
            {{ isLoading ? "加载中..." : "刷新数据" }}
          </button>
        </div>

      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from 'vue'

// 天气数据接口
interface WeatherData {
  temperature: number
  city: string
  air_quality: string
  humidity: number
  visibility: number
  pm25_outdoor: number
  pm25_indoor: number
  negative_ions: number
  publish_time: string
}

// 响应式状态
const isDeviceRunning = ref(true)
const isPowerOn = ref(true)
const currentLevel = ref(1)
const weatherData = ref<WeatherData | null>(null)
const isLoading = ref(true)

// 当前时间
const currentTime = computed(() => {
  return new Date().toLocaleString('zh-CN')
})

// 获取天气数据
const fetchWeatherData = async () => {
  try {
    isLoading.value = true
    const response = await fetch('/api/weather')
    if (response.ok) {
      const data = await response.json()
      weatherData.value = data
    } else {
      // 模拟数据
      weatherData.value = {
        temperature: 33,
        city: "深圳市",
        air_quality: "中",
        humidity: 52,
        visibility: 30,
        pm25_outdoor: 105,
        pm25_indoor: 14,
        negative_ions: 628,
        publish_time: "2025-06-09 15:07:03"
      }
    }
  } catch (error) {
    console.error('获取天气数据失败:', error)
    // 模拟数据
    weatherData.value = {
      temperature: 33,
      city: "深圳市", 
      air_quality: "中",
      humidity: 52,
      visibility: 30,
      pm25_outdoor: 105,
      pm25_indoor: 14,
      negative_ions: 628,
      publish_time: "2025-06-09 15:07:03"
    }
  } finally {
    isLoading.value = false
  }
}

// 电源开关
const handlePowerToggle = () => {
  isPowerOn.value = !isPowerOn.value
  isDeviceRunning.value = isPowerOn.value
}

// 档位切换
const handleLevelChange = () => {
  if (isPowerOn.value) {
    currentLevel.value = currentLevel.value >= 3 ? 1 : currentLevel.value + 1
  }
}

// 档位文字
const getLevelText = (level: number) => {
  const levels = { 1: "低", 2: "中", 3: "高" }
  return levels[level as keyof typeof levels] || "低"
}

// 定时器
let interval: number | null = null

// 生命周期
onMounted(() => {
  fetchWeatherData()
  interval = setInterval(fetchWeatherData, 5 * 60 * 1000) // 5分钟刷新
})

onUnmounted(() => {
  if (interval) clearInterval(interval)
})
</script>
