<template>
  <div class="circular-container">
    <img
      class="outer-ellipse"
      alt="Outer ellipse"
      src="https://c.animaapp.com/mdqqarhondzElL/img/ellipse-11.svg"
    />

    <img
      class="middle-ellipse"
      alt="Middle ellipse"
      src="https://c.animaapp.com/mdqqarhondzElL/img/ellipse-12.svg"
    />

    <img
      class="inner-ellipse"
      alt="Inner ellipse"
      src="https://c.animaapp.com/mdqqarhondzElL/img/ellipse-13.svg"
    />
  </div>
</template>

<style scoped>
.circular-container {
  @apply absolute w-full top-0 left-0;
}

.outer-ellipse {
  @apply absolute w-[311px] h-[311px] top-4 left-10 rounded-full aspect-square object-cover;
}

.middle-ellipse {
  @apply absolute w-[231px] h-[231px] top-[56px] left-[50px] rounded-full aspect-square object-cover;
}

.inner-ellipse {
  @apply absolute w-[175px] h-[175px] top-[84px] left-[78px] rounded-full aspect-square object-cover;
}
</style>
