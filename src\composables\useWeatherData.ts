import { ref, onUnmounted } from 'vue'

export interface WeatherData {
  temperature: number
  city: string
  air_quality: string
  humidity: number
  visibility: number
  pm25_outdoor: number
  pm25_indoor: number
  negative_ions: number
  publish_time: string
}

export function useWeatherData() {
  const weatherData = ref<WeatherData | null>(null)
  const isLoading = ref(true)
  const error = ref<string | null>(null)
  
  let pollInterval: number | null = null

  const fetchWeatherData = async () => {
    try {
      isLoading.value = true
      error.value = null
      
      const response = await fetch('/api/weather')
      if (response.ok) {
        const data = await response.json()
        weatherData.value = data
      } else {
        throw new Error('API请求失败')
      }
    } catch (err) {
      console.error('获取天气数据失败:', err)
      error.value = err instanceof Error ? err.message : '未知错误'
      
      // 使用模拟数据作为后备
      weatherData.value = {
        temperature: 33,
        city: "深圳市",
        air_quality: "中",
        humidity: 52,
        visibility: 30,
        pm25_outdoor: 105,
        pm25_indoor: 14,
        negative_ions: 628,
        publish_time: "2025-06-09 15:07:03"
      }
    } finally {
      isLoading.value = false
    }
  }

  const startPolling = (intervalMs = 5 * 60 * 1000) => {
    stopPolling()
    pollInterval = setInterval(fetchWeatherData, intervalMs)
  }

  const stopPolling = () => {
    if (pollInterval) {
      clearInterval(pollInterval)
      pollInterval = null
    }
  }

  // 清理定时器
  onUnmounted(() => {
    stopPolling()
  })

  return {
    weatherData,
    isLoading,
    error,
    fetchWeatherData,
    startPolling,
    stopPolling
  }
}
