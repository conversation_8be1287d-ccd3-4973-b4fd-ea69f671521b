<template>
  <div class="iphone-container">
    <!-- 状态栏组件 -->
    <StatusBar />
    
    <div class="main-content">
      <!-- 背景和装饰元素 -->
      <BackgroundElements />
      
      <!-- 空气质量可视化区域 -->
      <AirQualityVisualization 
        :air-quality="airQuality"
        :protection-days="protectionDays"
      />
      
      <!-- 数据指标区域 -->
      <MetricsGrid :metrics="airQualityMetrics" />
      
      <!-- 天气信息卡片 -->
      <WeatherCard 
        :weather-data="weatherData"
        :loading="isLoading"
        @refresh="handleRefresh"
      />
      
      <!-- 设备控制面板 -->
      <ControlPanel
        :device-state="deviceState"
        @power-toggle="handlePowerToggle"
        @level-change="handleLevelChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useWeatherData } from '@/composables/useWeatherData'
import { useDeviceControl } from '@/composables/useDeviceControl'
import StatusBar from '@/components/StatusBar.vue'
import BackgroundElements from '@/components/BackgroundElements.vue'
import AirQualityVisualization from '@/components/AirQualityVisualization.vue'
import MetricsGrid from '@/components/MetricsGrid.vue'
import WeatherCard from '@/components/WeatherCard.vue'
import ControlPanel from '@/components/ControlPanel.vue'

// 使用组合式函数管理状态
const { weatherData, isLoading, fetchWeatherData } = useWeatherData()
const { deviceState, handlePowerToggle, handleLevelChange } = useDeviceControl()

// 计算属性
const airQuality = computed(() => ({
  level: 25,
  status: '空气优',
  description: '车内综合空气质量'
}))

const protectionDays = computed(() => 231)

const airQualityMetrics = computed(() => [
  {
    id: 'pm25',
    value: "014",
    label: "PM2.5(µg/m³）",
    width: "w-[73px]",
  },
  {
    id: 'formaldehyde',
    value: "36", 
    label: "甲醛(µg/m³）",
    width: "w-[65px]",
  },
  {
    id: 'negative_ions',
    value: "25500",
    label: "负氧离子(个/cm³）",
    width: "w-[88px]",
  },
])

// 事件处理
const handleRefresh = () => {
  fetchWeatherData()
}

// 生命周期
onMounted(() => {
  fetchWeatherData()
})
</script>

<style scoped>
.iphone-container {
  @apply bg-neutral-100 flex flex-row justify-center w-full;
}

.main-content {
  @apply bg-neutral-100 w-[393px] h-[852px] relative;
}
</style>
