<template>
  <div class="min-h-screen bg-blue-100 p-8">
    <div class="max-w-md mx-auto bg-white rounded-lg shadow-lg p-6">
      <h1 class="text-2xl font-bold text-center mb-4">Vue测试页面</h1>
      <p class="text-center text-gray-600">如果你看到这个页面，说明Vue工作正常！</p>
      
      <div class="mt-6 text-center">
        <button 
          @click="count++" 
          class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded"
        >
          点击次数: {{ count }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const count = ref(0)
</script>
