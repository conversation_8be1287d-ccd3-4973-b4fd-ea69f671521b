<template>
  <div style="padding: 20px; background: lightblue; min-height: 100vh;">
    <h1 style="color: red; font-size: 24px;">Vue测试页面</h1>
    <p>如果你看到这个页面，说明Vue工作正常！</p>
    <button @click="count++" style="background: blue; color: white; padding: 10px; margin: 10px;">
      点击次数: {{ count }}
    </button>
    <div>当前时间: {{ new Date().toLocaleString() }}</div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const count = ref(0)
</script>
