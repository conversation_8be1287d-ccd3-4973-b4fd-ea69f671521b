<template>
  <div class="bg-neutral-100 flex flex-row justify-center w-full" data-model-id="107:9">
    <div class="bg-neutral-100 w-[393px] h-[852px] relative">
      <!-- Status bar -->
      <img
        class="absolute w-[375px] h-11 top-0 left-[11px]"
        alt="Status bar"
        src="https://c.animaapp.com/mdqqarhondzElL/img/---------.svg"
      />

      <div class="absolute w-[393px] h-[783px] top-[69px] left-0">
        <!-- Background image -->
        <img
          class="absolute w-[393px] h-[619px] top-[164px] left-0 object-cover"
          alt="Background"
          src="https://c.animaapp.com/mdqqarhondzElL/img/----.png"
        />

        <!-- Circular gradient elements -->
        <div class="absolute w-full top-0 left-0">
          <img
            class="absolute w-[311px] h-[311px] top-4 left-10 rounded-full aspect-square object-cover"
            alt="Outer ellipse"
            src="https://c.animaapp.com/mdqqarhondzElL/img/ellipse-11.svg"
          />

          <img
            class="absolute w-[231px] h-[231px] top-[56px] left-[50px] rounded-full aspect-square object-cover"
            alt="Middle ellipse"
            src="https://c.animaapp.com/mdqqarhondzElL/img/ellipse-12.svg"
          />

          <img
            class="absolute w-[175px] h-[175px] top-[84px] left-[78px] rounded-full aspect-square object-cover"
            alt="Inner ellipse"
            src="https://c.animaapp.com/mdqqarhondzElL/img/ellipse-13.svg"
          />

          <!-- Car visualization -->
          <img
            class="absolute w-[315px] h-[210px] top-[69px] left-[39px] object-cover"
            alt="Car visualization"
            src="https://c.animaapp.com/mdqqarhondzElL/img/---2.png"
          />

          <!-- Air quality indicator -->
          <div class="absolute w-[75px] h-24 top-[49px] left-[166px]">
            <div class="absolute w-[69px] h-[87px] top-[9px] left-0">
              <div class="top-0 left-1 text-[#454545] text-xl whitespace-nowrap absolute [font-family:'HarmonyOS_Sans-Regular',Helvetica] font-normal tracking-[0] leading-[normal]">
                空气优
              </div>

              <div class="top-[17px] left-0 text-[#1b705f] text-6xl absolute [font-family:'HarmonyOS_Sans-Regular',Helvetica] font-normal tracking-[0] leading-[normal] whitespace-nowrap">
                25
              </div>
            </div>

            <div class="top-0 left-2.5 text-[#454545] text-[6px] whitespace-nowrap absolute [font-family:'HarmonyOS_Sans-Regular',Helvetica] font-normal tracking-[0] leading-[normal]">
              车内综合空气质量
            </div>
          </div>

          <!-- Air quality logo -->
          <img
            class="absolute w-[177px] h-[90px] top-[58px] left-[105px]"
            alt="Air quality logo"
            src="https://c.animaapp.com/mdqqarhondzElL/img/group-13.png"
          />

          <!-- Mask group overlay -->
          <img
            class="absolute w-[349px] h-[250px] top-0 left-[22px]"
            alt="Mask group"
            src="https://c.animaapp.com/mdqqarhondzElL/img/mask-group.png"
          />
        </div>

        <!-- Health protection days -->
        <div class="top-64 left-[109px] text-transparent text-[15px] absolute [font-family:'HarmonyOS_Sans-Regular',Helvetica] font-normal tracking-[0] leading-[normal]">
          <span class="text-[#5b5b5b]">已为您健康守护 </span>
          <span class="text-[#5b5b5b] text-[19px]">231</span>
          <span class="text-[#5b5b5b]"> 天</span>
        </div>

        <!-- Cleaning reminder setting -->
        <div class="top-[282px] left-[149px] text-transparent text-[15px] whitespace-nowrap absolute [font-family:'HarmonyOS_Sans-Regular',Helvetica] font-normal tracking-[0] leading-[normal]">
          <span class="text-[#5b5b5b]">设置 </span>
          <span class="text-[#ff8800]">清洗提醒</span>
        </div>

        <!-- Air quality metrics -->
        <div class="absolute w-[298px] h-[45px] top-80 left-[42px] flex justify-between">
          <div v-for="(metric, index) in airQualityMetrics" :key="index" :class="`h-[45px] ${metric.width}`">
            <div class="relative h-full">
              <div class="absolute top-0 left-1.5 [font-family:'HarmonyOS_Sans-Regular',Helvetica] font-normal text-[#494949] text-[28px] tracking-[0] leading-[normal] whitespace-nowrap">
                {{ metric.value }}
              </div>
              <div class="absolute top-[33px] left-0 [font-family:'HarmonyOS_Sans-Regular',Helvetica] font-normal text-[#909090] text-[10px] tracking-[0] leading-[normal] whitespace-nowrap">
                {{ metric.label }}
              </div>
            </div>
          </div>
        </div>
