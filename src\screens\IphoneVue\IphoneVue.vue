<template>
  <div class="iphone-container">
    <div class="iphone-frame">
      <!-- 状态栏组件 -->
      <StatusBar />

      <div class="main-content">
        <!-- 背景图片 -->
        <BackgroundImage />

        <!-- 圆形渐变装饰元素 -->
        <CircularElements />

        <!-- 汽车可视化 -->
        <CarVisualization />

        <!-- 空气质量中心指示器 -->
        <AirQualityCenter />

        <!-- 遮罩层 -->
        <MaskOverlay />

        <!-- 健康守护信息 -->
        <HealthProtection :days="protectionDays" />

        <!-- 清洗提醒设置 -->
        <CleaningReminder />

        <!-- 空气质量指标网格 -->
        <MetricsGrid :metrics="airQualityMetrics" />

        <!-- 天气信息卡片 -->
        <WeatherInfoCard
          :weather-data="weatherData"
          :loading="isLoading"
          @refresh="handleRefresh"
        />

        <!-- 设备控制面板 -->
        <DeviceControlPanel
          :device-state="deviceState"
          :level-text="levelText"
          @power-toggle="handlePowerToggle"
          @level-change="handleLevelChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted } from 'vue'
import { useWeatherData } from '../../composables/useWeatherData'
import { useDeviceControl } from '../../composables/useDeviceControl'

// 导入Vue组件
import StatusBar from '../../components/iphone/StatusBar.vue'
import BackgroundImage from '../../components/iphone/BackgroundImage.vue'
import CircularElements from '../../components/iphone/CircularElements.vue'
import CarVisualization from '../../components/iphone/CarVisualization.vue'
import AirQualityCenter from '../../components/iphone/AirQualityCenter.vue'
import MaskOverlay from '../../components/iphone/MaskOverlay.vue'
import HealthProtection from '../../components/iphone/HealthProtection.vue'
import CleaningReminder from '../../components/iphone/CleaningReminder.vue'
import MetricsGrid from '../../components/iphone/MetricsGrid.vue'
import WeatherInfoCard from '../../components/iphone/WeatherInfoCard.vue'
import DeviceControlPanel from '../../components/iphone/DeviceControlPanel.vue'

// 使用组合式函数管理状态
const { weatherData, isLoading, fetchWeatherData, startPolling } = useWeatherData()
const { deviceState, levelText, handlePowerToggle, handleLevelChange } = useDeviceControl()

// 计算属性
const protectionDays = computed(() => 231)

const airQualityMetrics = computed(() => [
  {
    value: "014",
    label: "PM2.5(µg/m³）",
    width: "w-[73px]",
  },
  {
    value: "36",
    label: "甲醛(µg/m³）",
    width: "w-[65px]",
  },
  {
    value: "25500",
    label: "负氧离子(个/cm³）",
    width: "w-[88px]",
  },
])

// 事件处理方法
const handleRefresh = () => {
  fetchWeatherData()
}

// 生命周期
onMounted(() => {
  fetchWeatherData()
  startPolling()
})
</script>

<style scoped>
.iphone-container {
  @apply bg-neutral-100 flex flex-row justify-center w-full;
}

.iphone-frame {
  @apply bg-neutral-100 w-[393px] h-[852px] relative;
}

.main-content {
  @apply absolute w-[393px] h-[783px] top-[69px] left-0;
}
</style>
