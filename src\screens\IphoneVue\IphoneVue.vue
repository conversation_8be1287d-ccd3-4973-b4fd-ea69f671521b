<template>
  <div class="bg-neutral-100 flex flex-row justify-center w-full min-h-screen">
    <div class="bg-neutral-100 w-[393px] h-[852px] relative overflow-hidden">
      <!-- Status bar -->
      <div class="absolute w-[375px] h-11 top-0 left-[11px] bg-black/10 rounded-lg flex items-center justify-between px-4">
        <span class="text-black text-sm font-medium">9:41</span>
        <div class="flex items-center gap-1">
          <div class="w-4 h-2 bg-green-500 rounded-sm"></div>
          <div class="w-6 h-3 border border-black rounded-sm">
            <div class="w-4 h-1 bg-black rounded-sm m-0.5"></div>
          </div>
        </div>
      </div>

      <div class="absolute w-[393px] h-[783px] top-[69px] left-0">
        <!-- Main circular gradient background -->
        <div class="absolute w-full top-0 left-0">
          <!-- Large outer gradient circle -->
          <div class="absolute w-[350px] h-[350px] top-0 left-[22px] rounded-full opacity-60"
               style="background: radial-gradient(circle, rgba(34, 197, 94, 0.3) 0%, rgba(59, 130, 246, 0.25) 30%, rgba(251, 146, 60, 0.2) 60%, rgba(251, 146, 60, 0.1) 80%, transparent 100%)">
          </div>

          <!-- Medium gradient circle -->
          <div class="absolute w-[280px] h-[280px] top-[35px] left-[57px] rounded-full opacity-50"
               style="background: radial-gradient(circle, rgba(34, 197, 94, 0.4) 0%, rgba(59, 130, 246, 0.3) 40%, rgba(251, 146, 60, 0.15) 70%, transparent 100%)">
          </div>

          <!-- Inner gradient circle -->
          <div class="absolute w-[200px] h-[200px] top-[75px] left-[97px] rounded-full opacity-40"
               style="background: radial-gradient(circle, rgba(34, 197, 94, 0.5) 0%, rgba(59, 130, 246, 0.2) 50%, transparent 100%)">
          </div>

          <!-- Floating particles with enhanced animations -->
          <div class="absolute w-2 h-2 bg-green-300/60 rounded-full top-[80px] left-[120px] animate-pulse-glow"></div>
          <div class="absolute w-1 h-1 bg-blue-300/50 rounded-full top-[120px] left-[180px] animate-pulse-glow" style="animation-delay: 0.3s;"></div>
          <div class="absolute w-1.5 h-1.5 bg-green-400/40 rounded-full top-[100px] left-[280px] animate-pulse-glow" style="animation-delay: 0.7s;"></div>
          <div class="absolute w-1 h-1 bg-orange-300/60 rounded-full top-[140px] left-[100px] animate-pulse-glow" style="animation-delay: 0.5s;"></div>
          <div class="absolute w-2 h-2 bg-blue-400/50 rounded-full top-[160px] left-[250px] animate-pulse-glow" style="animation-delay: 1s;"></div>
          <div class="absolute w-1 h-1 bg-green-300/70 rounded-full top-[90px] left-[320px] animate-pulse-glow" style="animation-delay: 0.2s;"></div>

          <!-- Air quality indicator -->
          <div class="absolute w-[120px] h-24 top-[49px] left-[137px] text-center z-10">
            <div class="text-[#454545] text-[11px] font-normal mb-1 leading-tight">车内综合空气质量</div>
            <div class="text-[#454545] text-lg font-normal mb-1">空气优</div>
            <div class="text-[#1b705f] text-6xl font-bold leading-none">25</div>
          </div>

          <!-- Car visualization with better styling -->
          <div class="absolute w-[315px] h-[210px] top-[69px] left-[39px] flex items-center justify-center z-5 animate-float">
            <!-- Car shadow -->
            <div class="absolute w-72 h-8 bg-black/10 rounded-full bottom-4 blur-sm"></div>

            <!-- Main car body -->
            <div class="relative w-72 h-36 bg-gradient-to-br from-blue-200 via-blue-300 to-blue-400 rounded-3xl shadow-xl transform perspective-1000 rotate-y-12">
              <!-- Car roof/windshield -->
              <div class="absolute top-3 left-8 right-8 h-12 bg-gradient-to-b from-blue-100/80 to-blue-200/60 rounded-2xl border border-blue-300/30"></div>

              <!-- Car doors -->
              <div class="absolute top-8 left-4 w-16 h-16 bg-blue-300/50 rounded-lg border-l-2 border-blue-400/40"></div>
              <div class="absolute top-8 right-4 w-16 h-16 bg-blue-300/50 rounded-lg border-r-2 border-blue-400/40"></div>

              <!-- Front and rear -->
              <div class="absolute top-6 left-1 w-3 h-20 bg-blue-400 rounded-l-xl"></div>
              <div class="absolute top-6 right-1 w-3 h-20 bg-blue-400 rounded-r-xl"></div>

              <!-- Wheels -->
              <div class="absolute -bottom-3 left-8 w-8 h-8 bg-gray-700 rounded-full shadow-lg">
                <div class="absolute inset-1 bg-gray-500 rounded-full">
                  <div class="absolute inset-1 bg-gray-300 rounded-full"></div>
                </div>
              </div>
              <div class="absolute -bottom-3 right-8 w-8 h-8 bg-gray-700 rounded-full shadow-lg">
                <div class="absolute inset-1 bg-gray-500 rounded-full">
                  <div class="absolute inset-1 bg-gray-300 rounded-full"></div>
                </div>
              </div>

              <!-- Car highlights -->
              <div class="absolute top-2 left-4 w-4 h-2 bg-white/60 rounded-full"></div>
              <div class="absolute top-2 right-4 w-4 h-2 bg-white/60 rounded-full"></div>
            </div>
          </div>
        </div>

        <!-- Health protection days -->
        <div class="absolute top-64 left-[109px] text-center">
          <span class="text-[#5b5b5b] text-[18px]">已为您健康守护 </span>
          <span class="text-[#5b5b5b] text-[22px] font-medium">231</span>
          <span class="text-[#5b5b5b] text-[18px]"> 天</span>
        </div>

        <!-- Cleaning reminder setting -->
        <div class="absolute top-[282px] left-[149px] text-center">
          <span class="text-[#5b5b5b] text-[18px]">设置 </span>
          <span class="text-[#ff8800] text-[18px] font-medium cursor-pointer hover:underline">清洗提醒</span>
        </div>

        <!-- Air quality metrics -->
        <div class="absolute w-[298px] h-[45px] top-80 left-[42px] flex justify-between">
          <div v-for="(metric, index) in airQualityMetrics" :key="index" :class="`h-[45px] ${metric.width}`">
            <div class="relative h-full text-center">
              <div class="text-[#494949] text-[32px] font-normal leading-none">
                {{ metric.value }}
              </div>
              <div class="text-[#909090] text-[12px] font-normal mt-1">
                {{ metric.label }}
              </div>
            </div>
          </div>
        </div>

        <!-- Weather and air quality card -->
        <div class="absolute h-[183px] top-[409px] left-[23px] w-[345px] bg-white/80 backdrop-blur-md rounded-2xl shadow-lg overflow-hidden">
          <!-- Background grass texture -->
          <div class="absolute inset-0 bg-gradient-to-b from-green-50 via-green-100 to-green-200 opacity-40"></div>

          <!-- Weather content -->
          <div class="relative z-10 h-full p-4">
            <!-- Top section with location and temperature -->
            <div class="flex justify-between items-start mb-3">
              <div class="flex-1">
                <div class="text-[#2d5a3d] text-3xl font-bold leading-none">深圳市</div>
                <div class="text-[#666] text-xs mt-1">2025-06-09 15:07:03发布</div>
              </div>
              <div class="text-center mx-4">
                <div class="text-[#2d5a3d] text-5xl font-bold leading-none">33°C</div>
              </div>
              <div class="text-right text-sm leading-tight flex-1">
                <div class="text-[#666] text-xs">空气质量: 中</div>
                <div class="text-[#666] text-xs">湿度: 52%</div>
                <div class="text-[#666] text-xs">能见度: 30km</div>
              </div>
            </div>

            <!-- Separator line -->
            <div class="w-full h-px bg-gray-300/50 my-3"></div>

            <!-- Bottom section with air quality data -->
            <div class="flex justify-between items-center">
              <div class="text-center flex-1">
                <div class="text-[#ff8800] text-3xl font-bold leading-none">105</div>
                <div class="text-[#666] text-xs mt-1">车外PM2.5</div>
              </div>
              <div class="text-center flex-1">
                <div class="text-[#22c55e] text-3xl font-bold leading-none">14</div>
                <div class="text-[#666] text-xs mt-1">车内PM2.5</div>
              </div>
              <div class="text-center flex-1">
                <div class="text-[#374151] text-3xl font-bold leading-none">628</div>
                <div class="text-[#666] text-xs mt-1">车外负氧离子</div>
              </div>
            </div>
          </div>

          <!-- Decorative elements -->
          <div class="absolute bottom-0 left-0 w-full h-8 bg-gradient-to-t from-green-200/30 to-transparent"></div>
          <div class="absolute top-2 right-4 w-2 h-2 bg-yellow-300 rounded-full opacity-60"></div>
          <div class="absolute top-6 right-8 w-1 h-1 bg-blue-300 rounded-full opacity-40"></div>
        </div>

        <!-- Interactive Control panel -->
        <div class="absolute h-[74px] top-[637px] left-[22px] w-[345px] bg-white/95 backdrop-blur-md rounded-2xl shadow-xl border border-white/20">
          <div class="h-full flex items-center justify-between px-6">
            <!-- Device Status -->
            <div class="flex flex-col items-center justify-center">
              <div
                :class="`text-xs px-6 py-2.5 rounded-full font-medium transition-all duration-300 shadow-md ${
                  isDeviceRunning
                    ? 'bg-gradient-to-r from-emerald-500 to-emerald-600 text-white hover:from-emerald-600 hover:to-emerald-700'
                    : 'bg-gradient-to-r from-slate-400 to-slate-500 text-white hover:from-slate-500 hover:to-slate-600'
                }`"
              >
                设备{{ isDeviceRunning ? '运行' : '停止' }}
              </div>
            </div>

            <!-- Power Button -->
            <div class="flex flex-col items-center justify-center">
              <button
                @click="handlePowerToggle"
                :class="`w-14 h-14 rounded-full transition-all duration-300 shadow-lg hover:shadow-xl active:scale-95 flex items-center justify-center border-2 ${
                  isPowerOn
                    ? 'bg-gradient-to-br from-orange-400 to-orange-600 text-white hover:from-orange-500 hover:to-orange-700 border-orange-300 shadow-orange-200'
                    : 'bg-gradient-to-br from-gray-300 to-gray-500 text-gray-600 hover:from-gray-400 hover:to-gray-600 border-gray-200'
                }`"
              >
                <svg width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5">
                  <path d="M18.36 6.64a9 9 0 1 1-12.73 0"/>
                  <line x1="12" y1="2" x2="12" y2="12"/>
                </svg>
              </button>
            </div>

            <!-- Level Control -->
            <div class="flex flex-col items-center justify-center">
              <button
                @click="handleLevelChange"
                :disabled="!isPowerOn"
                :class="`px-6 py-2.5 text-sm font-bold rounded-full transition-all duration-200 active:scale-90 min-w-[56px] shadow-md ${
                  isPowerOn
                    ? 'bg-gradient-to-br from-blue-500 to-blue-600 text-white hover:from-blue-600 hover:to-blue-700 border border-blue-400 shadow-blue-200'
                    : 'bg-gray-100 text-gray-300 cursor-not-allowed border border-gray-200'
                }`"
              >
                {{ getLevelText(currentLevel) }}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

// Device control states
const isDeviceRunning = ref(true)
const isPowerOn = ref(true)
const currentLevel = ref(1)

// Air quality metrics data
const airQualityMetrics = [
  {
    value: "014",
    label: "PM2.5(µg/m³）",
    width: "w-[73px]",
  },
  {
    value: "36", 
    label: "甲醛(µg/m³）",
    width: "w-[65px]",
  },
  {
    value: "25500",
    label: "负氧离子(个/cm³）", 
    width: "w-[88px]",
  },
]

// Handle power button click
const handlePowerToggle = () => {
  isPowerOn.value = !isPowerOn.value
  if (!isPowerOn.value) {
    isDeviceRunning.value = false
  } else {
    isDeviceRunning.value = true
  }
}

// Handle level change - cycle through levels
const handleLevelChange = () => {
  if (isPowerOn.value) {
    currentLevel.value = currentLevel.value >= 3 ? 1 : currentLevel.value + 1
  }
}

// Get level display text
const getLevelText = (level: number) => {
  switch (level) {
    case 1:
      return "低"
    case 2:
      return "中"
    case 3:
      return "高"
    default:
      return "低"
  }
}
</script>

<style scoped>
.bg-gradient-radial {
  background: radial-gradient(circle, var(--tw-gradient-stops));
}

/* Custom animations */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes pulse-glow {
  0%, 100% {
    opacity: 0.6;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.1);
  }
}

@keyframes rotate-slow {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

.animate-rotate-slow {
  animation: rotate-slow 20s linear infinite;
}

/* Perspective for 3D effect */
.perspective-1000 {
  perspective: 1000px;
}

.rotate-y-12 {
  transform: rotateY(12deg);
}

/* Gradient backgrounds */
.bg-air-quality {
  background: radial-gradient(circle at center,
    rgba(34, 197, 94, 0.3) 0%,
    rgba(59, 130, 246, 0.25) 30%,
    rgba(251, 146, 60, 0.2) 60%,
    rgba(251, 146, 60, 0.1) 80%,
    transparent 100%);
}
</style>
