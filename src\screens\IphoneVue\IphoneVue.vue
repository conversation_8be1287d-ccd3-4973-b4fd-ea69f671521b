<template>
  <div class="bg-neutral-100 flex flex-row justify-center w-full" data-model-id="107:9">
    <div class="bg-neutral-100 w-[393px] h-[852px] relative">
      <!-- Status bar -->
      <img
        class="absolute w-[375px] h-11 top-0 left-[11px]"
        alt="Status bar"
        src="https://c.animaapp.com/mdqqarhondzElL/img/---------.svg"
      />

      <div class="absolute w-[393px] h-[783px] top-[69px] left-0">
        <!-- Background image -->
        <img
          class="absolute w-[393px] h-[619px] top-[164px] left-0 object-cover"
          alt="Background"
          src="https://c.animaapp.com/mdqqarhondzElL/img/----.png"
        />

        <!-- Circular gradient elements -->
        <div class="absolute w-full top-0 left-0">
          <img
            class="absolute w-[311px] h-[311px] top-4 left-10 rounded-full aspect-square object-cover"
            alt="Outer ellipse"
            src="https://c.animaapp.com/mdqqarhondzElL/img/ellipse-11.svg"
          />

          <img
            class="absolute w-[231px] h-[231px] top-[56px] left-[50px] rounded-full aspect-square object-cover"
            alt="Middle ellipse"
            src="https://c.animaapp.com/mdqqarhondzElL/img/ellipse-12.svg"
          />

          <img
            class="absolute w-[175px] h-[175px] top-[84px] left-[78px] rounded-full aspect-square object-cover"
            alt="Inner ellipse"
            src="https://c.animaapp.com/mdqqarhondzElL/img/ellipse-13.svg"
          />

          <!-- Car visualization -->
          <img
            class="absolute w-[315px] h-[210px] top-[69px] left-[39px] object-cover"
            alt="Car visualization"
            src="https://c.animaapp.com/mdqqarhondzElL/img/---2.png"
          />

          <!-- Air quality indicator -->
          <div class="absolute w-[75px] h-24 top-[49px] left-[166px]">
            <div class="absolute w-[69px] h-[87px] top-[9px] left-0">
              <div class="top-0 left-1 text-[#454545] text-xl whitespace-nowrap absolute [font-family:'HarmonyOS_Sans-Regular',Helvetica] font-normal tracking-[0] leading-[normal]">
                空气优
              </div>

              <div class="top-[17px] left-0 text-[#1b705f] text-6xl absolute [font-family:'HarmonyOS_Sans-Regular',Helvetica] font-normal tracking-[0] leading-[normal] whitespace-nowrap">
                25
              </div>
            </div>

            <div class="top-0 left-2.5 text-[#454545] text-[6px] whitespace-nowrap absolute [font-family:'HarmonyOS_Sans-Regular',Helvetica] font-normal tracking-[0] leading-[normal]">
              车内综合空气质量
            </div>
          </div>

          <!-- Air quality logo -->
          <img
            class="absolute w-[177px] h-[90px] top-[58px] left-[105px]"
            alt="Air quality logo"
            src="https://c.animaapp.com/mdqqarhondzElL/img/group-13.png"
          />

          <!-- Mask group overlay -->
          <img
            class="absolute w-[349px] h-[250px] top-0 left-[22px]"
            alt="Mask group"
            src="https://c.animaapp.com/mdqqarhondzElL/img/mask-group.png"
          />
        </div>

        <!-- Health protection days -->
        <div class="top-64 left-[109px] text-transparent text-[15px] absolute [font-family:'HarmonyOS_Sans-Regular',Helvetica] font-normal tracking-[0] leading-[normal]">
          <span class="text-[#5b5b5b]">已为您健康守护 </span>
          <span class="text-[#5b5b5b] text-[19px]">231</span>
          <span class="text-[#5b5b5b]"> 天</span>
        </div>

        <!-- Cleaning reminder setting -->
        <div class="top-[282px] left-[149px] text-transparent text-[15px] whitespace-nowrap absolute [font-family:'HarmonyOS_Sans-Regular',Helvetica] font-normal tracking-[0] leading-[normal]">
          <span class="text-[#5b5b5b]">设置 </span>
          <span class="text-[#ff8800]">清洗提醒</span>
        </div>

        <!-- Air quality metrics -->
        <div class="absolute w-[298px] h-[45px] top-80 left-[42px] flex justify-between">
          <div v-for="(metric, index) in airQualityMetrics" :key="index" :class="`h-[45px] ${metric.width}`">
            <div class="relative h-full">
              <div class="absolute top-0 left-1.5 [font-family:'HarmonyOS_Sans-Regular',Helvetica] font-normal text-[#494949] text-[28px] tracking-[0] leading-[normal] whitespace-nowrap">
                {{ metric.value }}
              </div>
              <div class="absolute top-[33px] left-0 [font-family:'HarmonyOS_Sans-Regular',Helvetica] font-normal text-[#909090] text-[10px] tracking-[0] leading-[normal] whitespace-nowrap">
                {{ metric.label }}
              </div>
            </div>
          </div>
        </div>

        <!-- Weather and air quality card -->
        <div class="absolute h-[183px] top-[409px] left-[23px] w-[345px] bg-white/60 backdrop-blur-sm rounded-2xl overflow-hidden">
          <div class="relative h-full px-4 py-4">
            <!-- Refresh icon -->
            <div class="absolute top-3 right-3 text-orange-400 text-[9px] [font-family:'HarmonyOS_Sans-Regular',Helvetica]">刷新</div>

            <!-- Weather data layout -->
            <div class="flex flex-col h-full justify-center px-6 py-4">
              <div v-if="isLoading" class="flex items-center justify-center h-full">
                <div class="text-green-600 text-sm animate-pulse">加载中...</div>
              </div>
              <div v-else>
                <!-- Top row: 3-column grid -->
                <div class="grid grid-cols-3 gap-4 mb-8 items-start">
                  <!-- Column 1: City info -->
                  <div class="flex flex-col justify-start">
                    <div class="text-green-700 text-[24px] font-bold leading-tight [font-family:'HarmonyOS_Sans-Regular',Helvetica]">
                      {{ weatherData?.city || "深圳市" }}
                    </div>
                    <div class="text-green-600 text-[9px] font-normal leading-tight mt-1 [font-family:'HarmonyOS_Sans-Regular',Helvetica]">
                      {{ weatherData?.publish_time || "2025-06-09 15:07:03" }}发布
                    </div>
                  </div>

                  <!-- Column 2: Temperature -->
                  <div class="flex justify-center items-start">
                    <div class="text-green-700 text-[48px] font-bold leading-none [font-family:'HarmonyOS_Sans-Regular',Helvetica]">
                      {{ weatherData?.temperature || 33 }}°C
                    </div>
                  </div>

                  <!-- Column 3: Air quality -->
                  <div class="flex flex-col items-end text-right text-green-600 text-[9px] font-normal leading-tight [font-family:'HarmonyOS_Sans-Regular',Helvetica]">
                    <div>空气质量: {{ weatherData?.air_quality || "中" }}</div>
                    <div>湿度: {{ weatherData?.humidity || 52 }}%</div>
                    <div>能见度: {{ weatherData?.visibility || 30 }}km</div>
                  </div>
                </div>

                <!-- Bottom row: 3-column grid -->
                <div class="grid grid-cols-3 gap-4 items-end">
                  <!-- Column 1: 车外PM2.5 -->
                  <div class="flex flex-col items-center">
                    <div class="text-green-600 text-[9px] font-normal mb-2 [font-family:'HarmonyOS_Sans-Regular',Helvetica]">车外PM2.5</div>
                    <div class="text-orange-500 text-[36px] font-bold leading-none [font-family:'HarmonyOS_Sans-Regular',Helvetica]">
                      {{ weatherData?.pm25_outdoor || "105" }}
                    </div>
                  </div>

                  <!-- Column 2: 车内PM2.5 -->
                  <div class="flex flex-col items-center">
                    <div class="text-green-600 text-[9px] font-normal mb-2 [font-family:'HarmonyOS_Sans-Regular',Helvetica]">车内PM2.5</div>
                    <div class="text-emerald-500 text-[36px] font-bold leading-none [font-family:'HarmonyOS_Sans-Regular',Helvetica]">
                      {{ weatherData?.pm25_indoor || "14" }}
                    </div>
                  </div>

                  <!-- Column 3: 车外负氧离子 -->
                  <div class="flex flex-col items-center">
                    <div class="text-green-600 text-[9px] font-normal mb-2 [font-family:'HarmonyOS_Sans-Regular',Helvetica]">车外负氧离子</div>
                    <div class="text-green-700 text-[36px] font-bold leading-none [font-family:'HarmonyOS_Sans-Regular',Helvetica]">
                      {{ weatherData?.negative_ions || "628" }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Control panel -->
        <div class="absolute h-[74px] top-[637px] left-[22px] w-[345px] border-none shadow-lg bg-white/95 backdrop-blur-md rounded-2xl">
          <div class="p-0 h-full">
            <div class="flex items-center h-full">
              <!-- Device Status -->
              <div class="flex-1 flex flex-col items-center justify-center h-full">
                <div :class="`text-xs px-4 py-2 rounded-full font-medium transition-all duration-300 ${
                  deviceState.isRunning
                    ? 'bg-emerald-500 text-white shadow-md hover:bg-emerald-600'
                    : 'bg-slate-400 text-white shadow-md hover:bg-slate-500'
                }`">
                  设备{{ deviceState.isRunning ? "运行" : "停止" }}
                </div>
              </div>

              <!-- Power Button -->
              <div class="flex-1 flex flex-col items-center justify-center h-full">
                <button
                  @click="handlePowerToggle"
                  :class="`w-12 h-12 rounded-full transition-all duration-300 shadow-lg hover:shadow-xl active:scale-95 ${
                    deviceState.isPowerOn
                      ? 'bg-gradient-to-br from-orange-400 to-orange-600 text-white hover:from-orange-500 hover:to-orange-700 border-2 border-orange-300'
                      : 'bg-gradient-to-br from-gray-300 to-gray-500 text-gray-600 hover:from-gray-400 hover:to-gray-600 border-2 border-gray-200'
                  }`"
                >
                  ⚡
                </button>
              </div>

              <!-- Level Control -->
              <div class="flex-1 flex flex-col items-center justify-center h-full">
                <button
                  @click="handleLevelChange"
                  :disabled="!deviceState.isPowerOn"
                  :class="`px-4 py-2 text-sm font-bold rounded-full transition-all duration-200 active:scale-90 min-w-[48px] ${
                    deviceState.isPowerOn
                      ? 'bg-gradient-to-br from-blue-500 to-blue-600 text-white shadow-md hover:from-blue-600 hover:to-blue-700 border border-blue-400'
                      : 'bg-gray-50 text-gray-300 cursor-not-allowed border border-gray-200'
                  }`"
                >
                  {{ levelText }}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted } from 'vue'
import { useWeatherData } from '../../composables/useWeatherData'
import { useDeviceControl } from '../../composables/useDeviceControl'

// 使用组合式函数管理状态
const { weatherData, isLoading, fetchWeatherData, startPolling } = useWeatherData()
const { deviceState, levelText, handlePowerToggle, handleLevelChange } = useDeviceControl()

// 空气质量指标数据
const airQualityMetrics = computed(() => [
  {
    value: "014",
    label: "PM2.5(µg/m³）",
    width: "w-[73px]",
  },
  {
    value: "36",
    label: "甲醛(µg/m³）",
    width: "w-[65px]",
  },
  {
    value: "25500",
    label: "负氧离子(个/cm³）",
    width: "w-[88px]",
  },
])

// 生命周期
onMounted(() => {
  fetchWeatherData()
  startPolling()
})
</script>
